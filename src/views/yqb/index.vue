<template>
  <div style="min-width: 1500px;">
    <el-card shadow="never" style="padding-top: 0">
      <div style="background: #fff; height: 100%; display: flex">
        <div style="flex: 1; min-width: 0">
          <div class="center-view temp-input" style="padding: 24px 32px">
            <div class="center-view" style="width: 100%; height: 147px; position: relative">
              <img style="width: 100%" src="@/assets/images/business/search_bg.png" />

              <div class="search center-align">
                <el-input class="temp-search-input" @change="searchChange" v-model="queryParams.searchWord"
                  placeholder="请输入关键词搜索" style="width: 100%">
                  <template #prefix>
                    <img src="@/assets/images/business/search.png" />
                  </template>
                </el-input>
              </div>
            </div>

            <!-- <a-input
              @clear="searchChange"
              @press-enter="searchChange"
              v-model="queryParams.name"
              :style="{ width: '100%' }"
              placeholder="请输入关键词搜索"
              allow-clear
            >
              <template #prefix>
                <img src="@/assets/images/business/search.png" />
              </template>
            </a-input> -->
          </div>

          <el-scrollbar style="min-width: 0; width: calc(100% - 60px); height: 120px; margin: 0 30px">
            <div style="display: flex">
              <div class="center-align" style="gap: 10px; border-bottom: 1px solid #eceded; padding-bottom: 24px">
                <div @click="classifyClick(item)" v-for="(item, index) in classifyList" :key="index"
                  class="center-view classify-item"
                  :class="{ 'classify-item-active': queryParams.categoryId === item.id }">
                  <img style="width: 48px; height: 48px; margin-bottom: 4px" :src="item.logo" />
                  <div style="color: #303133; line-height: 22px">
                    {{ item.name }}
                  </div>
                </div>
              </div>
            </div>
          </el-scrollbar>

          <div style="
              padding: 25px 30px;
              display: flex;
              flex-wrap: wrap;
              row-gap: 16px;
            column-gap: 16px;
            ">
            <div class="ent-item" v-for="(item, index) in entList" :key="index" @click="toEnt(item)">
              <div class="" style="display:flex;">
                <img :src="item.enterpriseLogo" style="width: 104px; height: 104px; margin-right: 14px;border-radius: 16px;" />

                <div>
                  <div class="title textover">
                    {{ item.enterpriseName }}
                  </div>
                  <div v-if="item.address" class="address textover">
                    {{ item.address }}
                  </div>

                  <div v-if="item.platformTags" style="display: flex;margin-top: 7px;">
                    <div v-for="(tagItem, tagIndex) in item.platformTags.split(',').slice(0, 3)" :key="tagIndex"
                      class="platform-tags center-view">
                      {{ tagItem }}
                    </div>
                    <div v-if="item.platformTags.split(',').length > 3" style="width: 22px;"
                      class="platform-tags center-view">
                      ...
                    </div>
                  </div>
                  <div v-if="!item.platformTags && item.tags" style="display: flex;margin-top: 7px;">
                    <div v-for="(tagItem, tagIndex) in item.tags.split(',').slice(0, 3)" :key="tagIndex"
                      class="tags center-view">
                      {{ tagItem }}
                    </div>
                  </div>

                  <div style="display: flex;margin-top: 8px;">

                    <div class="recent center-view">
                      <img style="width: 16px; height: 16px; margin-right: 1px"
                        src="@/assets/images/business/recent.png" />

                      近期服务{{ item.totalOrderCount }}

                    </div>

                    <div v-if="item.platformTags && item.tags" style="display: flex;">
                      <div v-for="(tagItem, tagIndex) in item.tags.split(',').slice(0, 2)" :key="tagIndex"
                        class="tags center-view">
                        {{ tagItem }}
                      </div>
                    </div>

                  </div>


                </div>

              </div>
              <div class="center-align" style="margin-top: 14px">
                <div style="width:118px;"></div>

                <div style="flex:1;height: 1px;background: #ECEDED;"></div>

              </div>
              <div v-for="(productItem, productIndex) in item.productList" :key="productIndex"
                class="product-item center-align">
                <div style="width:104px;margin-right: 14px;display: flex;justify-content: flex-end;flex-shrink: 0;">
                  <div class="center-align" v-if="productItem.rebateRatioStr && productItem.chargeRule == 2"
                    style="width: 74px;height:20px;position: relative;justify-content: flex-end;">
                    <img src="@/assets/images/business/rebate.png"
                      style="width: 74px;height:20px;position: absolute;top: 0;left: 0;" />
                    <div class="center-align" style="position: absolute;z-index: 2;">
                      <div style="font-size: 12px;color: #FFFFFF;line-height: 17px;">
                        {{ productItem.rebateRatioStr }}
                      </div>
                      <img style="width: 4px;height: 8px;margin-left: 2px;margin-right: 5px;"
                        src="@/assets/images/business/arrow_right_s.png" />
                      <img style="width:14px;height:14px;margin-right: 3px;" src="@/assets/images/business/back.png" />
                    </div>
                  </div>
                </div>
                <div class="center-align" style="flex:1;">
                  <img style="width:20px;height:20px;margin-right: 8px;" src="@/assets/images/business/product.png" />
                  <div class="textover" style="max-width: 200px;font-size: 14px;color: #303133;line-height: 20px;">
                    {{ productItem.price }}元
                    {{ productItem.productName }}
                  </div>
                </div>



              </div>


            </div>


          </div>
          <div class="center-view">
            <el-pagination background layout="total, prev, pager, next,jumper" :total="entTotal"
              :page-size="queryParams.size" v-model:current-page="queryParams.current" @change="pageChange" />
            <!-- <a-pagination
              :page-size="queryParams.size"
              :current="queryParams.current"
              @change="pageChange"
              :total="productTotal"
            /> -->
          </div>
          <div style="height: 20px"></div>
        </div>

        <div style="width: 400px; border-left: 1px solid #eceded; flex-shrink: 0">
          <div style="padding: 14px 0px 24px 0px">
            <!-- <a-tabs default-active-key="1" @change="rankingTabsChange">
              <a-tab-pane key="1" title="畅销排行"></a-tab-pane>
              <a-tab-pane key="2" title="专属推荐"> </a-tab-pane>
            </a-tabs> -->
            <div style="padding: 0 30px">
              <el-tabs v-model="rankingQueryParams.sortType" @tab-click="rankingTabsChange">
                <el-tab-pane label="畅销排行" :name="1"></el-tab-pane>
                <el-tab-pane label="专属推荐" :name="2"></el-tab-pane>
              </el-tabs>
              <div style="height: 10px"></div>
            </div>

            <el-scrollbar :style="`height: calc(750px + ${entList.length > 6 ? '230px' : '0px'})`"
              style="overflow: auto" ref="rankingScrollbarRef" @scroll="handleRankingScroll">
              <div style="padding: 0 30px">
                <div v-for="(item, index) in rankingList" :key="index" @click="productDetailShow(item)"
                  style="display: flex; margin-bottom: 30px; cursor: pointer">
                  <img :src="item.logo" style="width: 62px; height: 62px; margin-right: 15px;flex-shrink: 0;border-radius: 16px;" />

                  <div>
                    <div class="card-title">
                      {{ item.name }}
                    </div>
                    <div class="center-align" style="margin-top: 8px">
                      <div class="center-align" v-if="item.rebateRatioStr && item.chargeRule == 2"
                        style="width: 74px;height:20px;position: relative;justify-content: flex-start;margin-right: 8px;">
                        <img src="@/assets/images/business/rebate2.png"
                          style="width: 74px;height:20px;position: absolute;top: 0;left: 0;" />
                        <div class="center-align" style="position: absolute;z-index: 2;">


                          <img style="width:14px;height:14px;margin-left: 3px;"
                            src="@/assets/images/business/back.png" />
                          <div style="font-size: 12px;color: #FFFFFF;line-height: 17px;margin-left: 3px;">
                            {{ item.rebateRatioStr }}
                          </div>

                          <img style="width: 4px;height: 8px;margin-left: 4px;"
                            src="@/assets/images/business/arrow_right_s.png" />
                        </div>
                      </div>
                      <div class="center-view" style="
                        height: 20px;
                        background: #ffeae3;
                        border-radius: 4px;
                        padding: 0 4px;
                        margin-right: 8px;
                      ">
                        <img style="width: 16px; height: 16px; margin-right: 2px"
                          src="@/assets/images/business/star_red.png" />

                        <div style="
                          font-size: 12px;
                          color: #ff5757;
                          line-height: 17px;
                        ">
                          {{ item.score }}
                        </div>
                      </div>
                      <div class="center-view" style="
                        height: 20px;
                        background: #e1f0ff;
                        border-radius: 4px;
                        padding: 0 4px;
                      ">
                        <img style="width: 16px; height: 16px; margin-right: 2px"
                          src="@/assets/images/business/category.png" />

                        <div style="
                          font-size: 12px;
                          color: #3370ff;
                          line-height: 17px;
                        ">
                          {{ item.categoryName }}
                        </div>
                      </div>
                    </div>

                    <div class="center-align" style="
                      margin-top: 8px;
                      font-size: 12px;
                      color: #606266;
                      line-height: 17px;
                      flex-wrap: wrap;
                    ">
                      <div class="center-align" v-for="(tagsItem, tagsIndex) in item.tags.split('、')" :key="tagsIndex">
                        {{ tagsItem }}
                        <div v-if="tagsIndex < item.tags.split('、').length - 1" style="margin: 0 8px">|
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-card>






    <!-- 侧边我的订单模块 -->
    <div class="side-suspension center-view" @click="orderListShow">
      <img src="@/assets/images/business/order.png" />

      <div style="
          font-size: 14px;
          color: #ffffff;
          line-height: 18px;
          writing-mode: vertical-lr;
          text-orientation: upright;
          margin: 4px 0;
        ">
        我的订单
      </div>

      <img src="@/assets/images/business/arrow_left.png" />
    </div>

    <el-drawer :size="600" v-model="orderListVisible" destroy-on-close>
      <template #title> 我的订单 </template>

      <div>
        <el-tabs v-model="orderListQueryParams.status" @tab-click="orderListStatusChange">
          <el-tab-pane label="全部" :name="undefined"></el-tab-pane>
          <el-tab-pane label="待接单" :name="1"></el-tab-pane>
          <el-tab-pane label="跟进中" :name="2"></el-tab-pane>
          <el-tab-pane label="待支付" :name="3"></el-tab-pane>
          <el-tab-pane label="订单完成" :name="4"></el-tab-pane>
          <el-tab-pane label="订单取消" :name="5"></el-tab-pane>
        </el-tabs>
      </div>

      <el-scrollbar ref="orderScrollbarRef" @scroll="handleOrderScroll"
        style="height: calc(100vh - 204px); overflow: auto">
        <div v-for="(item, index) in orderList" :key="index">
          <div class="center-align" style="justify-content: space-between; font-size: 14px">
            <div style="color: #303133; line-height: 20px"> 订单编号：{{ item.orderNum }} </div>

            <div v-if="item.status === 1 || item.status === 2" style="color: #3370ff">
              {{ item.statusName }}</div>
            <div v-if="item.status === 3" style="color: #ff5757"> {{ item.statusName }}</div>
            <div v-if="item.status === 4 || item.status === 5" style="color: #909399">
              {{ item.statusName }}</div>
          </div>
          <div class="center-align" style="margin-top: 15px; justify-content: space-between; align-items: flex-end">
            <div class="center-align">
              <img style="width: 62px; height: 62px; border-radius: 16px; margin-right: 15px" :src="item.productLogo" />

              <div>
                <div class="textover" style="
                    font-weight: 500;
                    font-size: 16px;
                    color: #303133;
                    max-width: 380px;
                    line-height: 20px;
                  ">
                  {{ item.productName }}
                </div>
                <div style="font-size: 12px; color: #606266; line-height: 17px; margin-top: 2px">
                  金额：￥{{ item.orderAmount }}
                </div>
                <div style="font-size: 12px; color: #606266; line-height: 17px; margin-top: 2px">
                  创建时间：￥{{ item.createTime }}
                </div>
              </div>
            </div>

            <div style="height: 100%">
              <div v-if="item.status === 3" @click="doPayOrder(item)" class="center-view" style="
                  width: 80px;
                  height: 26px;
                  background: #3370ff;
                  border-radius: 4px;
                  font-size: 12px;
                  color: #ffffff;
                  line-height: 17px;
                  cursor: pointer;
                ">
                去支付
              </div>
            </div>
          </div>
          <div style="height: 1px; background:#eceded; margin: 24px 0"></div>
        </div>
      </el-scrollbar>
    </el-drawer>

    <productDetailDrawer :productDetailVisible="productDetailVisible" :product-detail="productDetail"
      @handleClose="productDetailClose"></productDetailDrawer>


    <el-dialog v-model="payVisible" title="立即支付" width="500">
      <div class="center-align" style="margin-top: 24px">
        <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
          购买业务：
        </div>
        <div class="center-align">
          <img style="width: 38px; height: 38px; margin-right: 10px" :src="productDetail.logo" />

          <div>
            <div>{{ productDetail.name }}</div>
            <div class="center-align" style="
                margin-top: 8px;
                font-size: 12px;
                color: #606266;
                line-height: 17px;
                max-width: 260px;
                flex-wrap: wrap;
              ">
              <div class="center-align" v-for="(tagsItem, tagsIndex) in productDetail.tags.split('、')" :key="tagsIndex">
                {{ tagsItem }}
                <div v-if="tagsIndex < productDetail.tags.split('、').length - 1" style="margin: 0 8px">|
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- <div class="center-align" style="margin-top: 20px">
        <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
          业务价格：
        </div>
        <div class="center-align" style="font-size: 14px; color: #ff5757; line-height: 20px">
          ￥{{ productDetail.price }}
        </div>
      </div> -->

      <div class="center-align" style="margin-top: 20px">
        <div style="width: 70px; font-weight: 500; font-size: 14px; color: #303133; line-height: 20px">
          支付方式：
        </div>
        <div class="center-align" style="font-size: 14px; color: #ff5757; line-height: 20px">
          <el-radio-group v-model="payForm.payType" @change="payTypeChange">
            <el-radio :label="1">微信</el-radio>
            <el-radio :label="2">支付宝</el-radio>
          </el-radio-group>
        </div>
      </div>

      <div style="
          height: 200px;
          background: #f6f6f6;
          margin-top: 20px;
          border-radius: 4px;
          padding: 16px 32px;
        ">
        <div style="font-weight: 500; font-size: 18px; color: #303133; line-height: 25px">
          扫码购买
        </div>

        <div style="display: flex; margin-top: 20px">
          <img style="width: 120px; height: 120px; margin-right: 24px" :src="pqyCode" />

          <div>
            <div style="
                font-weight: 500;
                font-size: 14px;
                color: #303133;
                line-height: 20px;
                margin-top: 64px;
              ">
              支付金额
            </div>

            <div style="
                display: flex;
                align-items: flex-end;
                margin-top: 14px;
                font-weight: 500;
                font-size: 14px;
                color: #ff5757;
                line-height: 20px;
              ">
              <div>￥</div>
              <div style="font-size: 20px">{{ orderAmount }}</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog v-model="paySuccessVisible" title="立即购买" width="500">
      <div class="center-view" style="padding: 83px 0 138px 0; flex-flow: column">
        <img style="width: 72px; height: 72px; margin-bottom: 16px" src="@/assets/images/business/success.png" />

        <div style="font-weight: 500; font-size: 18px; color: #303133; line-height: 25px">
          购买成功
        </div>

        <div style="font-size: 14px; color: #a2a3a5; line-height: 20px; margin-top: 8px">
          请稍等，商家后续会与您联系
        </div>
      </div>
    </el-dialog>

    <buyDialog :visible="buyDialogVisible" :productDetail="productDetail" @paySuccess="paySuccess"
      @handleClose="buyDialogClose" :tempActive="tempActive" :tempOrderDetail="tempOrderDetail"></buyDialog>

  </div>
</template>
<script lang="ts" setup>
import buyDialog from './components/buyDialog.vue';
import productDetailDrawer from './components/productDetail.vue';
import starYellow from '@/assets/images/business/star_yellow.png'
import starGray from '@/assets/images/business/star_gray.png'
import allClassify from '@/assets/images/business/all_classify.png'
import {
  addRecommendOrder,
  getProductPage,
  getProductDetail,
  getProductCategoryList,
  getProductCommentPage,
  postYqbOrderPay,
  getYqbOrderPayStatus,
  getYqbOrderPage,
  getYqbProductEntPage
} from '@/api/common'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { throttle } from 'lodash'
import AMapLoader from '@amap/amap-jsapi-loader';


const message = useMessage() // 消息弹窗
import router from '@/router';

const classifyList = ref([
  {
    createTime: '2025-02-20 11:27:59',
    createUser: '管理员',
    id: null,
    logo: allClassify,
    name: '全部'
  }
])


// 分页配置
const queryParams = reactive({
  current: 1,
  size: 9,
  searchWord: '',
  categoryId: null
})

const searchChange = async () => {
  queryParams.current = 1
  getEntList()
}

// 获取分类
const getProductCategory = async () => {
  const response = await getProductCategoryList()
  // console.log('分类列表=', response)
  if (response.code === 0) {
    classifyList.value = classifyList.value.concat(response.data)
  }
}

getProductCategory()

// const productList = ref([])
// const productTotal = ref(0)
const entList = ref([]);
const entTotal = ref(0);


const classifyClick = async (val) => {
  queryParams.categoryId = val.id
  queryParams.current = 1
  getEntList()
}

const deepClone = (obj) => {
  // 如果是 null 或非对象类型，直接返回
  if (obj === null || typeof obj !== 'object') return obj

  // 如果是数组，递归拷贝数组中的每个元素
  if (Array.isArray(obj)) return obj.map(deepClone)

  // 如果是对象，递归拷贝对象的每个属性
  const newObj = {}
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      newObj[key] = deepClone(obj[key])
    }
  }
  return newObj
}

// 获取分页列表
const getEntList = async () => {
  let tempQueryParams = deepClone(queryParams)
  if (!tempQueryParams.categoryId) {
    delete tempQueryParams.categoryId
  }
  if (!tempQueryParams.name) {
    delete tempQueryParams.name
  }

  const response = await getYqbProductEntPage(tempQueryParams)
  // console.log("分页列表=", response)
  if (response.code === 0) {
    // productList.value = response.data.records
    // productTotal.value = response.data.total
    entList.value = response.data.records;
    entTotal.value = response.data.total;
  }
}

getEntList()

// 分页变化
const pageChange = (current: number) => {
  console.log('pageChange=', current)
  queryParams.current = current
  getEntList()
}


// 获取排行tab变化  rankingTabsChange
const rankingScrollbarRef = ref() // 滚动条实例
const rankingNoMore = ref(false) // 是否无更多数据
const rankingQueryParams = reactive({
  current: 1,
  size: 10,
  sortType: 1
})

// 监听滚动事件
const handleRankingScroll = throttle((e) => {
  const { scrollTop, clientHeight, scrollHeight } = rankingScrollbarRef.value?.wrapRef
  // 距离底部 50px 时触发
  if (scrollHeight - (scrollTop + clientHeight) < 50) {
    if (rankingNoMore.value) return
    // console.log("handleRankingScroll")
    getRainking()
  }
}, 200)

const rankingTabsChange = async (val: any) => {
  // console.log('rankingTabsChange=', val)
  rankingScrollbarRef.value!.setScrollTop(0)
  rankingList.value = []
  await nextTick()

  setTimeout(() => {
    rankingNoMore.value = false
    rankingQueryParams.current = 1
    rankingQueryParams.sortType = val.props.name
    getRainking()
  }, 0)
}
const rankingList = ref([])
const getRainking = async () => {
  if (rankingNoMore.value) return
  const response = await getProductPage(rankingQueryParams)
  // console.log('排行分页列表=', response)
  if (response.code === 0) {
    // rankingList.value = response.data
    if (response.data.records.length === 0) {
      rankingNoMore.value = true
      return
    }
    rankingList.value = [...rankingList.value, ...response.data.records]
    if (rankingQueryParams.sortType == 1) {
      rankingNoMore.value = true
    }
    if (rankingQueryParams.sortType == 2) {
      rankingQueryParams.current += 1
    }
  }
}

getRainking(1)

const productDetailVisible = ref(false)
const productDetail = ref({})

const productDetailShow = async (val: any) => {
  const response = await getProductDetail({ id: val.id })
  // console.log("productDetail=", response)
  if (response.code === 0) {
    productDetail.value = response.data

    productDetailVisible.value = true
  }
}

const productDetailClose = async (val: any) => {
  console.log('productDetailClose', val);
  productDetailVisible.value = false;

};






// onMounted(() => {
//   console.log('触发yqb')
// })

// 我的订单模块
const orderListVisible = ref(false)
// 订单状态（1待接单 2跟进中 3订单待支付 4订单完成 5订单取消）
const orderListQueryParams = reactive({
  current: 1,
  size: 10,
  status: undefined
})

const orderList = ref([])
const orderNoMore = ref(false)
const orderScrollbarRef = ref() // 滚动条实例

const getOrderList = async () => {
  if (orderNoMore.value) return
  const response = await getYqbOrderPage(orderListQueryParams)
  // console.log('分页列表=', response)
  if (response.code === 0) {
    if (response.data.records.length === 0) {
      orderNoMore.value = true
      return
    }
    orderList.value = [...orderList.value, ...response.data.records]
    orderListQueryParams.current += 1
  }
}

const handleOrderScroll = throttle((e) => {
  const { scrollTop, clientHeight, scrollHeight } = orderScrollbarRef.value?.wrapRef
  // 距离底部 50px 时触发
  if (scrollHeight - (scrollTop + clientHeight) < 50) {
    if (orderNoMore.value) return
    // console.log("handleOrderScroll")
    getOrderList()
  }
}, 200)

const orderListStatusChange = (val: any) => {
  orderNoMore.value = true
  orderScrollbarRef.value!.setScrollTop(0)
  setTimeout(() => {
    console.log('orderListStatusChange=', val)
    orderListQueryParams.status = val.props.name
    orderListQueryParams.current = 1
    orderList.value = []
    orderNoMore.value = false
    getOrderList()
  }, 0)
}

const orderListShow = () => {
  orderListQueryParams.current = 1
  orderListQueryParams.status = undefined
  orderList.value = []
  orderNoMore.value = false
  getOrderList()
  orderListVisible.value = true
}

const tempActive = ref(0)
const tempOrderDetail = ref({})

const doPayOrder = async (val: any) => {

  if (val.type == 1) {
    tempActive.value = 1
  } else if (val.type == 2 || val.type == 3) {
    tempActive.value = 0
  }
  tempOrderDetail.value = val
  productDetail.value = val.productDetailVO
  buyDialogVisible.value = true


}

const buyDialogVisible = ref(false)
const buyDialogClose = () => {
  buyDialogVisible.value = false
}


const paySuccess = () => {




  orderNoMore.value = false
  orderList.value = []
  orderListQueryParams.current = 1

  getOrderList()

}





const payForm = reactive({
  id: null,
  payType: 1
})
const orderAmount = ref('')


const pqyCode = ref('')
const mchOrderNo = ref('')


// 获取支付二维码
const getPayCode = async (payType: number, type: number) => {
  payForm.payType = payType
  const response = await postYqbOrderPay(payForm)

  if (response.code === 0) {
    console.log('getPayCode res=', response)

    if (type == 2) {
      message.success('切换成功')
    }

    mchOrderNo.value = response.data.mchOrderNo
    pqyCode.value = response.data.url
    payVisible.value = true
  } else {
    message.error(response.msg)
  }
}

const payTypeChange = async (val: any) => {
  console.log('payTypeChange=', val)
  getPayCode(val, 2)
}



const payVisible = ref(false)

const paySuccessVisible = ref(false)

// 轮询相关变量
let pollTimer = null
const POLL_INTERVAL = 2000 // 2秒间隔

// 模拟 API 调用（替换为你的真实接口）
const fetchPaymentStatus = async () => {
  try {
    const response = await getYqbOrderPayStatus({ mchOrderNo: mchOrderNo.value }) // 替换为你的接口调用
    if (response.code === 0) {
      if (response.data && response.data.state === 2) {
        // 成功条件：停止轮询
        stopPolling()
        payVisible.value = false
        paySuccessVisible.value = true

        orderNoMore.value = false
        orderList.value = []
        orderListQueryParams.current = 1
        getEntList()
        getOrderList()
      }
    }
    return response
  } catch (error) {
    console.error('轮询请求失败:', error)
    // 失败时可根据需求处理（如重试机制）
    throw error
  }
}

// 启动轮询
const startPolling = () => {
  // 先清除可能存在的旧定时器
  stopPolling()

  // 立即执行第一次请求
  fetchPaymentStatus()

  // 设置定时器
  pollTimer = setInterval(async () => {
    if (!payVisible.value) {
      stopPolling()
      return
    }

    await fetchPaymentStatus()
  }, POLL_INTERVAL)
}

// 停止轮询
const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer)
    pollTimer = null

    console.log('轮询已停止')
  }
}

// 监听状态变化
watchEffect(() => {
  if (payVisible.value) {
    console.log('开始轮询')
    setTimeout(() => {
      startPolling()
    }, 2000)
  } else {
    stopPolling()
  }
})

// 组件卸载时自动清理
onUnmounted(stopPolling)




// 前往商家详情页面 
const toEnt = (val: any) => {
  let query = {
    enterpriseId: val.enterpriseId,
    categoryId: undefined
  }

  if (queryParams.categoryId) {
    query.categoryId = queryParams.categoryId
  }

  router.push({ name: 'yabBusiness', query: query });
};





const getCurrentPosition = () => {

navigator.geolocation.getCurrentPosition(
  (position) => {
    console.log('用户允许定位', position.coords.latitude, position.coords.longitude);
  },
  (error) => {
    console.error('用户拒绝定位', error);
  },
  {
    enableHighAccuracy: true,
    timeout: 5000,
    maximumAge: 0
  }
);

  window._AMapSecurityConfig = {
    securityJsCode: '30fa9db69ffc3ff071ae90b2a0c4c7f8', // 必填
  };

  AMapLoader.load({
    key: '13a7d78de06c94da74d157386c6d883e',
    version: '1.4.15',
    plugins: ['AMap.Geolocation'] // 按需加载插件
  }).then((AMap) => {
    const geolocation = new AMap.Geolocation({
      enableHighAccuracy: true, // 是否使用高精度定位，默认:true
    //   timeout: 100000, // 超过100秒后停止定位，默认：无穷大
      buttonOffset: new AMap.Pixel(10, 20), // 定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
      zoomToAccuracy: true, // 定位成功后调整地图视野范围以适应定位位置，默认：false
      buttonPosition: 'RB', // 定位按钮停靠位置，默认：'LB'，左下角
    });
    geolocation.getCurrentPosition((status, result) => {
      if (status === 'complete') {
        console.log('定位成功', result);
        const lnglat = result.position;
        console.log('当前经纬度:', lnglat.lng, lnglat.lat);
        localStorage.setItem('la', lnglat.lat)
        localStorage.setItem('lo', lnglat.lng)
      } else {
        console.log('定位失败', result);
      }
    });
  });
};
getCurrentPosition()



</script>
<style lang="less" scoped>
.search {
  position: absolute;
  width: 420px;
  height: 30px;
  background: #ffffff;
  border-radius: 15px;
  border: 1px solid #dcdfe6;
  padding: 0 10px;
  margin-bottom: -65px;
}

:deep(.temp-search-input) {
  .el-input__wrapper {
    box-shadow: none !important;
    padding: 0 !important;
  }
}

.classify-item {
  width: 96px;
  height: 90px;
  flex-flow: column;
  cursor: pointer;
}

.classify-item-active {
  width: 100px;
  height: 90px;
  background: #f1f7ff;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.card-title {
  font-weight: 500;
  font-size: 18px;
  color: #303133;
  line-height: 25px;
}


// :deep(.arco-tabs-nav::before) {
//   /* 在这里设置你想要的样式 */
//   border-bottom: none;
//   /* 示例：移除底部边框 */
//   background-color: transparent;
//   /* 示例：设置背景颜色 */
// }



:deep(.el-card__body) {
  padding: 0 !important;
}

:deep(.el-drawer__header) {
  margin-bottom: 0px !important;
  justify-content: space-between;
}

:deep(.el-drawer__header > :first-child) {
  flex: 0 1 auto !important;
}

.side-suspension {
  flex-flow: column;
  width: 42px;
  height: 156px;
  background: #3370ff;
  border-radius: 16px 1px 1px 16px;
  position: fixed;
  right: 0;
  top: calc(50vh - 78px);
  cursor: pointer;
  z-index: 999;
}

.ent-item {
  // flex: 1;
  width: calc((100% - 32px) / 3);
  flex-shrink: 0;
  // display: flex;
  cursor: pointer;
  height: 217px;
  background: #FFFFFF;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #DCDFE6;
  box-sizing: border-box;


  .title {
    font-weight: 500;
    font-size: 16px;
    color: #303133;
    line-height: 22px;
    max-width: 228px;
    margin-top: 2px;
  }

  .address {
    font-size: 12px;
    color: #909399;
    line-height: 17px;
    margin-top: 6px;
    max-width: 228px;
  }

  .tags {
    width: 60px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #DCDFE6;
    font-size: 12px;
    color: #909399;
    line-height: 17px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .platform-tags {
    width: 60px;
    height: 20px;
    background: #F4DFB5;
    border-radius: 4px;
    font-size: 12px;
    color: #9B7638;
    line-height: 17px;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .recent {
    flex-shrink: 0;
    padding: 0 6px;
    height: 20px;
    background: #FFEAE3;
    border-radius: 4px;
    font-size: 12px;
    color: #FF5757;
    line-height: 17px;
    margin-right: 8px;
  }

  .product-item {
    margin-top: 12px;
  }
}

.ent-item:hover {
  box-shadow: 0px 2px 15px 0px rgba(35, 63, 92, 0.21);
}
</style>
