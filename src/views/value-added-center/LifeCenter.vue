<template>
  <el-card class="app-center" shadow="never">
    <!-- 顶部标题 -->
    <h1 class="title">生活中心</h1>

    <!-- Banner区域 -->
    <div class="banner-container">
      <img class="banner" src="@/assets/image/bannerLifeCenter.png" alt="banner" />
      <div class="search-box">
        <el-input v-model="queryParams.keyWord" placeholder="请输入关键词搜索" @keyup.enter="getAllAppList">
          <template #prefix>
            <img src="@/assets/images/business/search.png" />
          </template>
        </el-input>
      </div>
    </div>

    <!-- 分类图标区域 -->
    <el-scrollbar>
      <div class="category-container">
        <div class="category-list">
          <div class="category-item" :class="currentCategoryIndex === index ? 'category-item-selected' : ''"
            v-for="(category, index) in categoryList" :key="index" @click="onCategoryClick(index)">
            <el-image :src="category.icon ? category.icon : categoryDefaultIcon" fit="cover"></el-image>
            <span>{{ category.name }}</span>
          </div>
        </div>
      </div>
    </el-scrollbar>

    <div class="line"></div>

    <!-- 分类应用列表 -->
    <div class="app-list" v-if="appList.length" v-loading="isLoading">
      <div class="app-item" v-for="app in appList" :key="app.id" @click="openUrl(app)">
        <el-image class="app-icon" :src="app.appIcon ? app.appIcon : appDefaultIcon" fit="cover" />
        <div class="app-info">
          <div class="app-name">{{ app.appName }}</div>
          <div class="app-description">{{ app.appDesc }}</div>
        </div>
      </div>
    </div>
    <div v-else class="center-align app-none">
      <img src="@/assets/image/payslip-bg.png"></img>
      <div class="text-area">
        <span>该分类暂无应用</span>
      </div>
    </div>
    <Pagination :total="totalRecord" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getAllAppList" />
  </el-card>
</template>

<script setup lang="ts">
import categoryDefaultIcon from '@/assets/image/appCenterDefaultIcon.png'
import appDefaultIcon from '@/assets/imgs/logoBlue.png';
import { applicationApi } from '@/api/application/list'
import { getLifeCenterAppListPage } from '@/api/common'
import { AppItem } from './index.vue'
import allClassify from '@/assets/images/business/all_classify.png'
const queryParams = ref<{
  pageNo: number,
  pageSize: number,
  keyWord: string,
  lifeCenterTypeId?: number | undefined
}>({
  pageNo: 1,
  pageSize: 10,
  keyWord: '',
})

type CategoryItem = {
  id?: number,
  name: string,
  icon?: string
}

const categoryList = ref<CategoryItem[]>([])
const currentCategoryIndex = ref(0)

const appList = ref<AppItem[]>([])
const totalRecord = ref(0)
const isLoading = ref(false)

// 分类列表
const getCategoryListAll = async () => {
  const res = await applicationApi.lifeTypeList({ type: 2 })
  categoryList.value = res
  categoryList.value.unshift({ name: '全部' ,icon: allClassify,})
}

// 生活应用列表 getLifeCenterAppList
const getAllAppList = async () => {
  isLoading.value = true
  try {
    const res = await getLifeCenterAppListPage(queryParams.value)
    appList.value = res.data.list ?? []
    totalRecord.value = res.data.total
  } catch {
    appList.value = []
    totalRecord.value = 0
  } finally {
    isLoading.value = false
  }
}

// 分类点击
const onCategoryClick = (index: number) => {
  currentCategoryIndex.value = index
  queryParams.value.lifeCenterTypeId = categoryList.value[index].id
  getAllAppList()
}

// 生活中心 - 打开应用
const openUrl = async (item: AppItem) => {
  window.open(item.pcHomepageLink, "_blank");
}

onMounted(async () => {
  await getCategoryListAll()
  getAllAppList()
})

</script>

<style scoped>
.app-center {
  width: 1160px;
  padding-bottom: 20px;
  margin: 0 auto;
  border-radius: 5px;
  text-align: center;
}

.line {
  margin-top: 20px;
  height: 1px;
  background: #eceded;
}

.title {
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  color: #303133
}

.banner-container {
  position: relative;
  width: 1120px;
  height: 147px;
}

.banner {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.search-box {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 420px;
  border-radius: 100%;
  border: 1px solid #DCDFE6;

  :deep(.el-input__wrapper) {
    border-radius: 50px;
  }
}

.category-container {
  padding: 10px 0;
  margin: 30px 0 10px 0;
}

.category-list {
  display: flex;
  gap: 10px;
}

.category-item {
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;

  .el-image {
    width: 48px;
    height: 48px;
    margin-bottom: 5px;
  }

  span {
    width: 80px;
    overflow: hidden;
    font-size: 14px;
    color: #303133;
  }
}

.category-item-selected {
  background-color: #F1F7FF;
  border: 1px solid #DCDFE6;
  border-radius: 8px;
}

.app-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-top: 20px;
}

.app-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 10px;
  overflow: hidden;
}

.app-item:hover {
  background-color: #eee;
}

.app-icon {
  width: 50px;
  height: 50px;
  margin-right: 15px;
}

.app-info {
  flex: 1;
  text-align: left;
}

.app-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.app-description {
  margin-top: 6px;
  width: 260px;
  height: 17px;
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.app-none {
  height: 200px;
  margin: 20px 0 30px 0;
  display: flex;
  justify-content: center;
  flex-direction: column;
  font-size: 14px;
  color: #303133;

  img {
    width: 100px;
  }

  .text-area {
    margin-top: 20px;
  }
}
</style>