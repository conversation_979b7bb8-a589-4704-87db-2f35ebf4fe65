<template>
  <el-card v-loading="loading" class="oaCard marginBottom" style="margin-top: 15px">
    <div class="group">
      <span class="cardTitle">应用中心</span>
      <div class="group-container" v-if="appList.length > 0">
        <div @click="goInfo(item)" v-for="(item, index) in appList" :key="index" v-show="isValidUrl(item)">
          <div class="group-item">
            <img :src="item.appIcon ? item.appIcon : logoBlue" class="logo-left" />
            <div>
              <ellipsis class="item-name" hover-tip :content="item.name" />
              <div class="tishi" v-if="item.appDesc">{{ item.appDesc }}</div>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else :image-size="100" description="" style="margin: auto" />
    </div>
  </el-card>
</template>

<script>
import logoBlue from '@/assets/imgs/logoBlue.png';
import { getGroupModelsByUser, getProcessCountData, getAppList } from '@/api/modelGroup'
import taskApi from '@/api/processTask'
import InitiateProcess from '../InitiateProcess.vue'
import processList from './myProcess.vue'
import draftsList from './myDrafts.vue'
// import message from '@/views/wflow/bb_oa/message.vue'
export default {
  name: 'ApprovalGroup',
  // props: {
  //   type: ''
  // },
  components: {
    InitiateProcess,
    processList,
    draftsList,
    // message
  },
  data() {
    return {
      logoBlue,
      taskCount: {
        todo: 0,
        mySubmited: 0,
        cc: 0
      },
      submitLoading: false,
      recentlyUsed: [],
      //searchResult: [],
      searchForm: '',
      loading: false,
      openItemDl: false,
      selectForm: {},
      formItem: {},
      formList: {
        list: [],
        inputs: '',
        searchResult: []
      },
      pending: {
        list: []
      },
      popupStyle: {
        height: '100%',
        width: '100%',
        background: '#f7f7f9'
      },
      activeName: '1',
      draftId: '',
      width: '57%',
      screenWidth: null,
      appList: []
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
    loginUser() {
      return this.$wflow.loginUser
    },
    searchResult() {
      let result = []
      this.formList.list.forEach((group) => {
        group.items.forEach((item) => {
          if (item.formName.indexOf(this.searchForm) > -1) {
            result.push(item)
          }
        })
      })
      return result
    }
  },
  watch: {
    screenWidth: function (n, o) {
      if (n > 1600) {
        this.width = '57%'
      }
      if (n <= 1600) {
        this.width = '70%'
      }
      if (n <= 1200) {
        this.width = '80%'
      }
      if (n <= 700) {
        this.width = '95%'
      }
    }
  },
  mounted() {
    this.getList()
    // this.getCountData()
    this.recentlyUsed = JSON.parse(
      localStorage.getItem(`recentlyUsed:${(this.loginUser || {}).id}`) || '[]'
    )

    this.screenWidth = document.body.clientWidth
    window.onresize = () => {
      this.screenWidth = document.body.clientWidth
    }
  },

  methods: {
    isValidUrl(item) {
      const trimmedUrl = item.pcHomepageLink.replace(/^https:\/\/\s*/, '').trim()
      return !!trimmedUrl
    },
    getList() {
      getAppList({
        pageNo: 1,
        pageSize: 100
      }).then((res) => {
        if (res.code == 0) {
          this.appList = res.data.list
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goInfo(item) {
      this.$router.push({
        path: '/talentBoLeLink/' + item.id,
        query: { link: encodeURIComponent(item.pcHomepageLink), names: item.name }
      })
      // this.$router.push('/talentBoLeLink?link=' + encodeURIComponent(item.homepageLink))
    }
  }
}
</script>

<style lang="less" scoped>
.cardTitle {
  margin-left: 10px;
}

.group {
  margin-left: -10px;

  .iconFixed {
    position: absolute;
    right: 0;
    top: 20px;
    cursor: pointer;
    color: #808080;
  }

  .group-container {
    display: flex;
    flex-wrap: wrap;
  }

  .group-title {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0 10px 10px;
  }

  .group-item {
    display: inline-block;
    cursor: pointer;
    border-radius: 8px;
    text-align: center;
    width: 175px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    padding: 10px;
    box-sizing: border-box;

    &:hover {
      background: #f6f6f6;
    }

    :deep(.icon) {
      padding: 8px;
      border-radius: 8px;
      font-size: 20px;
      color: #fff;
      background: #38adff;
      height: 20px;
      width: 20px;
      line-height: 20px;
      margin-right: 10px;
    }

    .item-name {
      font-size: 15px;
      color: #303133;
      text-align: left;
      margin-top: 0;
      overflow-wrap: break-word;
      white-space: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }

    .tishi {
      font-size: 12px;
      color: #a8a9aa;
      text-align: left;
      margin-top: 5px;
      overflow-wrap: break-word;
      white-space: normal;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
    }

    // & > div {
    //   display: flex;
    //   align-items: center;
    // }
    .rcbl {
      width: 36px;
      height: 36px;
      margin-right: 10px;
      border-radius: 8px;
    }
  }
}

.formsBox {
  position: relative;
}

.examineText {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .cardTitle {
    margin-bottom: 0;
  }
}

.message {
  position: absolute;
  right: 30px;
  top: 30px;
  cursor: pointer;
}

.logo-left {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  border: 1px solid #fafafa;
  box-sizing: border-box;
  margin-right: 8px;
}
</style>