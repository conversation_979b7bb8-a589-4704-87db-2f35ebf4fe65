<template>
  <div>
    <el-table :data="tableData" v-loading="loading" @row-click="showProcess">
      <el-table-column prop="processDefName" label="审批类型" min-width="150px" show-overflow-tooltip>
        <template v-slot="scope">
          <el-tag size="small" type="success" v-if="scope.row.superInstanceId !== scope.row.instanceId">子</el-tag>
          <span style="margin-left: 5px">{{ scope.row.processDefName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="staterUser" label="发起人" width="150px">
        <template v-slot="scope">
          <avatar :size="35" :name="scope.row.staterUser ? scope.row.staterUser.name : 'name'"
            :src="scope.row.staterUser ? scope.row.staterUser.avatar : ''" />
        </template>
      </el-table-column>
      <el-table-column prop="tenantName" label="公司名称" min-width="200px" show-overflow-tooltip />
      <el-table-column prop="startTime" label="提交时间" width="180px" />
      <el-table-column prop="finishTime" label="结束时间" width="180px" />
      <el-table-column prop="taskName" label="当前节点" width="110px" />
      <el-table-column prop="status" label="审批状态" width="140px">
        <template v-slot="scope">
          <el-tag :type="getProcTag(scope.row.result)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="duration" label="已耗时" min-width="180px">
        <template v-slot="scope">
          {{ getDuration(scope.row) }}
        </template>
      </el-table-column>
    </el-table>
    <div style="text-align: right">
      <el-pagination background :page-sizes="[10, 20, 30, 50, 100]" layout="total, sizes,prev, pager, next,jumper"
        :total="total" v-model:page-size="params.pageSize" v-model:current-page="params.pageNo"
        @update:current-page="handleSizeChange" @update:page-size="handleSizeChange" />
    </div>
    <el-drawer :size="isMobile ? '100%' : '560px'" direction="rtl" title="审批详情" v-model="processVisible"
      class="custom-detail-header">
      <instance-preview v-if="processVisible" :instance-id="selectInstance" @handler-after="handlerAfter" />
    </el-drawer>
  </div>
</template>

<script>
import taskApi from '@/api/processTask'
import moment from 'moment'
import InstancePreview from '@/views/wflow/workspace/approval/ProcessInstancePreview.vue'
import { getProcTag } from "@/utils/ProcessUtil.js";
export default {
  components: {
    InstancePreview

  },
  data() {
    return {
      loading: false,
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
      },
      tableData: [],
      selectInstance: '',
      processVisible: false,
      formName: ''
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.getList()
      },
    },
  },
  methods: {
    getName(e) {
      console.log(e)
      this.total = 0
      this.tableData = []
      this.formName = e
      this.getList()
    },
    getList() {
      this.loading = true
      taskApi.getProcessData({
        ...this.params,
        approvalType: this.formName,
      }).then((res) => {
        this.loading = false
        this.total = res.data.total
        this.tableData = res.data.records
      }).catch((e) => {
        this.loading = false
      })
    },
    getProcTag,
    getDuration(row) {
      const startDate = new Date(row.startTime)
      const endDate = row.finishTime ? new Date(row.finishTime) : new Date()
      const timeDifference = endDate - startDate

      const seconds = Math.max(Math.floor((timeDifference / 1000) % 60), 0)
      const minutes = Math.floor((timeDifference / 1000 / 60) % 60)
      const hours = Math.floor((timeDifference / (1000 * 60 * 60)) % 24)
      const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24))

      const parts = []
      if (days > 0) parts.push(`${days}天`)
      if (hours > 0) parts.push(`${hours}小时`)
      if (minutes > 0) parts.push(`${minutes}分钟`)
      if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`)
      // if (minutes > 0 || parts.length === 0) parts.push(`${minutes}分钟`)
      return parts.join('')
    },
    showProcess(row) {
      this.processVisible = true
      this.selectInstance = row.instanceId
    },
    handlerAfter() {
      this.processVisible = false
      this.getList()
    },
    handleSizeChange(val) {
      this.params.pageSize = val
    },
  }
}
</script>

<style scoped lang="less">
.el-pagination {
  justify-content: right;
  margin-top: 20px;
}
</style>