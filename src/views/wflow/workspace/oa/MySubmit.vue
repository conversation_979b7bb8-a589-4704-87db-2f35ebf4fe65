<template>
  <div>
    <div v-loading="loading" class="contentChild" v-if="!openItemDl">
      <el-form :inline="true">
        <el-form-item label="类型">
          <el-select
            v-model="approvalTypes"
            placeholder="请选择类型"
            filterable
            clearable
            remote
            :remote-method="hanlderNameFilterChange"
            remote-show-suffix
            multiple
            :loading="nameFilterLoading"
            class="!w-240px"
          >
            <el-option
              v-for="(item,findex) in filterNameList"
              :key="item.formId"
              :label="item.formName"
              :value="item.formName"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公司名称">
          <el-select
            v-model="params.companyName"
            placeholder="请输入公司名称"
            filterable
            clearable
            class="!w-220px mr-10px"
          >
            <el-option
              v-for="item in companyList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审批状态">
          <el-select
            v-model="params.status"
            placeholder="请选择审批状态"
            filterable
            clearable
            class="!w-220px mr-10px"
            @change="handleChange"
          >
            <el-option
              v-for="item in approvalList"
              :key="item.id"
              :label="item.name"
              :value="item.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="提交时间">
          <span class="textTi">
            <el-date-picker
              v-model="startTime"
              value-format="YYYY-MM-DD "
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="!w-220px"
              @change="selectStart"
            />
          </span>
        </el-form-item>
        <el-form-item label="结束时间">
          <span class="textTi">
            <el-date-picker
              v-model="endTime"
              value-format="YYYY-MM-DD "
              type="daterange"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="!w-220px"
              @change="selectEnter"
            />
          </span>
        </el-form-item>
        <el-form-item label="内容检索" prop="createTime">
          <el-input
            placeholder="请输入"
            clearable
            class="!w-240px mr-10px"
            @keyup.enter="handleChange"
            v-model="content"
            @clear="handleChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-search" @click="handleChange">搜索</el-button>
          <el-button
              @click="handleDown"
              icon="el-icon-download"
              type="success"
              plain
              v-loading.fullscreen.lock="loadingAll"
          >导出
          </el-button>
        </el-form-item>
      </el-form>

      <el-table :data="dataList" @row-click="showProcess">
        <el-table-column
          fixed
          prop="processDefName"
          label="审批类型"
          show-overflow-tooltip
          min-width="120px"
        >
          <template v-slot="scope">
            <el-tag
              size="small"
              type="success"
              v-if="scope.row.superInstanceId !== scope.row.instanceId"
              >子</el-tag
            >
            <span style="margin-left: 5px">{{ scope.row.processDefName }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed prop="content" label="摘要" min-width="200px">
          <template #default="scope">
            <el-tooltip placement="right" popper-class="custom-popper-class">
              <template #content>
                <div v-html="tooltipContent(scope.row.content)" class="customTooltip"></div>
              </template>
              <div class="contentScope">{{ scope.row.content }}</div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="staterUser" show-overflow-tooltip label="发起人" min-width="100px">
          <template v-slot="scope">
            <avatar
              :size="26"
              :name="scope.row.staterUser ? scope.row.staterUser.name : ''"
              :src="scope.row.staterUser ? scope.row.staterUser.avatar : ''"
              class="custom-avatar"
            />
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="tenantName"
          label="公司名称"
          min-width="160px"
        />
        <el-table-column
          show-overflow-tooltip
          prop="startTime"
          label="提交时间"
          min-width="120px"
        ></el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="finishTime"
          label="结束时间"
          min-width="120px"
        ></el-table-column>
        <el-table-column show-overflow-tooltip prop="taskName" label="当前节点"></el-table-column>
        <el-table-column prop="status" label="审批状态">
          <template v-slot="scope">
            <el-tag :type="getProcTag(scope.row.result)">{{ scope.row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="duration" label="已耗时" min-width="120px">
          <template v-slot="scope">
            {{ scope.row.execTime ? scope.row.execTime : getDuration(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作">
          <template v-slot="scope">
            <el-tooltip
              content="非本公司流程，请切换到对应公司下再次提交"
              placement="top-start"
              :disabled="scope.row.isCurTenant"
            >
              <el-button
                type="primary"
                link
                @click.stop="reSubmit(scope.row)"
                :disabled="!scope.row.isCurTenant"
                >再次提交
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div>
        <el-pagination
          background
          :page-sizes="[10, 20, 30, 50, 100]"
          layout="total, sizes,prev, pager, next,jumper"
          :total="total"
          :page-size="params.pageSize"
          v-model:current-page="params.pageNo"
          @size-change="handleSizeChange"
        ></el-pagination>
      </div>
      <el-drawer
        :size="isMobile ? '100%' : '560px'"
        direction="rtl"
        title="审批详情"
        :z-index="1000"
        v-model="processVisible"
        class="custom-detail-header"
      >
        <instance-preview
          v-if="processVisible"
          :instance-id="selectInstance.instanceId"
          :noId="selectInstance.nodeId"
          @handler-after="handlerAfter"
        ></instance-preview>
      </el-drawer>
    </div>
    <formModule ref="formModuleRef" :isOA="isOA" @close="formClose"></formModule>
  </div>
</template>

<script>
import taskApi, {mySubmittedExport} from '@/api/processTask'
import moment from 'moment'
import InstancePreview from '../approval/ProcessInstancePreview.vue'
import { getProcTag } from '@/utils/ProcessUtil.js'
import formModule from './formModule.vue'
import { useMyStore } from '@/store/modules/jump'
import download from "@/utils/download";

export default {
  name: 'MySubmit',
  props: {
    isOA: false
  },
  components: {
    InstancePreview,
    formModule
  },
  data() {
    return {
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
        finished: null,
        code: '',
        companyName: '',
        status: ''
      },
      openItemDl: false,
      selectInstance: {},
      loading: false,
      processVisible: false,
      formList: [],
      dataList: [],
      searchInput: '',
      startTime: [],
      endTime: [],
      activeName: '1',
      draftId: '',
      typeList: [],
      companyList: [],
      approvalList: [
        {
          name: '审批通过',
          id: 1
        },
        {
          name: '审批进行中',
          id: 2
        },
        {
          name: '审批被驳回',
          id: 3
        },
        {
          name: '审批被撤销',
          id: 4
        }
      ],
      width: '57%',
      screenWidth: null,
      content: '',
      approvalTypes: [],
      filterNameList:[],
      nameFilterLoading:false,
      loadingAll: false,
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    },
    launchSuccess() {
      const store = useMyStore()
      return store.launchSuccess
    }
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.getSubmittedList()
      }
    },
    screenWidth: function (n, o) {
      if (n > 1600) {
        this.width = '57%'
      }
      if (n <= 1600) {
        this.width = '70%'
      }
      if (n <= 1200) {
        this.width = '80%'
      }
      if (n <= 700) {
        this.width = '95%'
      }
    },
    launchSuccess(newValue, oldValue) {
      if (newValue) {
        this.openItemDl = false
        this.getSubmittedList()
      }
    }
  },
  mounted() {
    this.screenWidth = document.body.clientWidth
    window.onresize = () => {
      this.screenWidth = document.body.clientWidth
    }

    this.getGroupModelList()
    this.getSubmittedList()
    this.groupTenantList()
  },
  methods: {
    hanlderNameFilterChange(key){
      if(key){
        this.nameFilterLoading = true
        setTimeout(() => {
          this.nameFilterLoading = false
          this.filterNameList = this.typeList.filter(e=>{
            return e.formName.includes(key)
          })
        }, 100);
      }else{
        this.filterNameList = this.typeList
      }
    },
    tooltipContent(content) {
      return content.replace(/\n/g, '<br/>')
    },
    //  类型下拉
    getGroupModelList() {
      taskApi.groupModelList({tenantFlag:true}).then((res) => {
        this.typeList = res.data
      })
    },
    // 公司名称下拉
    groupTenantList() {
      taskApi.groupTenantList().then((res) => {
        this.companyList = res.data
      })
    },
    handleSizeChange(val) {
      this.params.pageSize = val
    },
    handleChange() {
      if (this.params.pageNo == 1) {
        this.getSubmittedList()
      } else {
        this.params.pageNo = 1
      }
    },
    selectEnter(v) {
      // if (!v) {
      this.getSubmittedList()
      // }
    },
    selectStart(v) {
      // if (!v) {
      this.getSubmittedList()
      // }
    },
    getProcTag,
    handleDown() {
      //加类型限制，以防导出数据过大，卡死
      if (!this.approvalTypes || this.approvalTypes.length === 0) {
        return this.$message.warning('请选择流程类型')
      }
      this.loadingAll = true
      let query = {
        ...this.params,
        approvalTypes: this.approvalTypes.join(';'),
        content: this.content,
        startTime: this.startTime && this.startTime.length > 0 ? this.startTime.join(',') : '',
        endTime: this.endTime && this.endTime.length > 0 ? this.endTime.join(',') : ''
      }
      taskApi
          .mySubmittedExport(query)
          .then((data) => {
            this.loadingAll = false
            download.excel(
                data,
                (this.approvalTypes && this.approvalTypes.length > 0 ? this.approvalTypes.join('、') : '审批流程') + '_导出数据.xlsx'
            )
          })
          .catch((e) => {
            this.loadingAll = false
          })
    },
    getSubmittedList() {
      this.loading = true
      let query = {
        ...this.params,
        approvalTypes: this.approvalTypes.join(';'),
        content: this.content,
        startTime: this.startTime && this.startTime.length > 0 ? this.startTime.join(',') : '',
        endTime: this.endTime && this.endTime.length > 0 ? this.endTime.join(',') : ''
      }
      taskApi
        .getUserSubmittedList(query)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((e) => {
          this.loading = false
        })
    },
    reSubmit(row) {
      if (this.isOA) {
        useMyStore().handleLaunchStatus(true)
      }

      this.openItemDl = true
      this.$refs.formModuleRef.open(row)
      console.log(row)
    },
    formClose(e) {
      this.openItemDl = false
      if (e == 'secondClose' && this.isOA) {
        useMyStore().handleLaunchStatus(false)
      }
    },
    showProcess(row) {
      this.processVisible = true
      this.selectInstance = row
    },
    getDuration(row) {
      const startDate = new Date(row.startTime)
      const endDate = row.finishTime ? new Date(row.finishTime) : new Date()
      const timeDifference = endDate - startDate

      const seconds = Math.max(Math.floor((timeDifference / 1000) % 60), 0)
      const minutes = Math.floor((timeDifference / 1000 / 60) % 60)
      const hours = Math.floor((timeDifference / (1000 * 60 * 60)) % 24)
      const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24))

      const parts = []
      if (days > 0) parts.push(`${days}天`)
      if (hours > 0) parts.push(`${hours}小时`)
      if (minutes > 0) parts.push(`${minutes}分钟`)
      if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`)
      // if (minutes > 0 || parts.length === 0) parts.push(`${minutes}分钟`)
      return parts.join('')
    },
    handlerAfter() {
      this.processVisible = false
      this.getSubmittedList()
    }
  }
}
</script>

<style scoped lang="less">
.contentChild {
  background: #fff;
  padding: 20px;
}

.el-pagination {
  justify-content: right;
  margin-top: 20px;
}

.searchBox {
  .el-form-item {
    margin-right: 10px;
  }
}

.textTi {
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
}

/deep/ .textTi .el-select__placeholder.is-transparent {
  color: #333 !important;
}

:deep(.el-form-item) {
  margin-right: 25px;
}

.formOaDiv {
  // margin-top: -100px;
}

.contentScope {
  white-space: pre-wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.custom-avatar .a-img > div) {
  font-size: 10px !important;
}
</style>
<style>
.customTooltip {
  min-width: 200px;
  max-width: 400px;
  white-space: pre-wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 20;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-popper-class {
  background: #45484a;
}

.custom-popper-class .el-popper__arrow::before {
  border: 1px solid #45484a;
  background: #45484a;
}
</style>
