<template>
  <div class="process-node-render" ref="tip">
    <div :class="{ 'process-node-error': error }" class="left">
      <div style="font-size: 16px">
        <span v-if="task.enableEdit" style="color: red"> * </span>
        {{ task.title }}
      </div>
      <span class="user-tip-span" v-if="taskUser" :key="task.desc">{{ task.desc }}</span>
    </div>
    <div class="right">
      <avatar :closeable="user.enableEdit" @close="delUser(i)" :size="38" showY :src="user.avatar" :type="user.type"
        :name="user.name" v-for="(user, i) in taskUser" :key="'user_' + i" :title="user.name" />
      <span class="add-user" v-if="task.enableEdit && (task.multiple || taskUser.length === 0)"
        @click="$emit('addUser', task)">
        <icon name="el-icon-plus" />
        <div>添加</div>
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: "ProcessNodeRender",
  components: {},
  props: {
    task: {
      type: Object,
      required: true,
      default: () => {
        return {}
      }
    },
    error: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      taskUser: null
    }
  },
  created() {
    // console.log(this.task, 'this.task')
    this.taskUser = this.task.users
  },

  methods: {
    delUser(i) {
      this.taskUser.splice(i, 1)
      this.$emit('delUser', this.task.id, i)
    },
    errorShark() {
      this.$refs.tip.classList.add('shake-tip')
      setTimeout(() => this.$refs.tip.classList.remove('shake-tip'), 1200)
    }
  }
}
</script>

<style lang="less" scoped>
.process-node-error {
  color: @theme-danger;
}

// .add-user {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   flex-direction: column;

//   .icon {
//     padding: 10px;
//     font-size: 16px;
//     border-radius: 50%;
//     border: 1px dashed #8c8c8c;
//     cursor: pointer;

//     &:hover {
//       color: @theme-primary;
//       border: 1px dashed @theme-primary;
//     }
//   }
// }

.add-user {
  // display: flex;
  // width: 38px;
  // height: 38px;
  box-sizing: border-box;
  // border: 1px solid #e4e7ed;
  // border-radius: 50%;
  // align-items: center;
  // justify-content: center;
  margin-right: 5px;
  text-align: center;
  font-size: 14px;

  .icon {
    padding: 10px;
    font-size: 16px;
    border-radius: 6px;
    border: 1px dashed #8c8c8c;
    cursor: pointer;

    &:hover {
      color: @theme-primary;
      border: 1px dashed @theme-primary;
    }
  }
}

.shake-tip {
  animation: shake 1s ease-in-out;
}

//水平抖动提示
@keyframes shake {

  10%,
  90% {
    transform: translate3d(-1px, 0, 0);
  }

  20%,
  80% {
    transform: translate3d(+2px, 0, 0);
  }

  30%,
  70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%,
  60% {
    transform: translate3d(+4px, 0, 0);
  }

  50% {
    transform: translate3d(-4px, 0, 0);
  }
}

.user-tip-span {
  font-size: 12px;
  color: #a8adaf;
}

.process-node-render {
  display: flex;
  width: 100%;

  .left {
    flex: 1;
  }

  .right {
    width: 60%;
    display: flex;
    overflow: auto;
    padding: 0 1px;
    margin-bottom: 10px;
    flex-wrap: wrap;
    justify-content: flex-end;
    align-items: flex-start;

    :deep(.avatar) {
      margin-bottom: 10px;
      margin-left: 4px;
    }
  }
}

:deep(.avatar .a-img > div) {
  font-size: 12px;
}

:deep(.avatar .name) {
  font-size: 14px;
  margin-top: 2px;
  // white-space: normal;
  // word-break: break-all;
  line-height: 120%;
}
</style>
