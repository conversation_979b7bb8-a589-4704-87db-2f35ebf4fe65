export const ValueType = {
  string: 'String',
  object: 'Object',
  array: 'Array',
  number: 'Number',
  date: 'Date',
  user: 'User',
  dept: 'Dept',
  dateRange: 'DateRange',
  getValidType: (type) => {
    switch (type) {
      case 'Dept':
      case 'User':
      case 'DateRange':
        return 'array'
      case 'Array':
      case 'Number':
      case 'String':
        return type.toLowerCase()
      default:
        return undefined
    }
  }
}

export const groupComponents = [
  {
    name: '假勤管理',
    components: [
      {
        title: '请假/调休',
        icon: 'el-icon-clock',
        itemType:'leaveType',
        name:'',
        props:{},
        child:[
          // {
          //   title: '请假类型',
          //   name: 'SelectInput',
          //   icon: 'el-icon-circlecheck',
          //   value: '',
          //   valueType: ValueType.string,
          //   props: {
          //     required: true,
          //     enablePrint: true,
          //     expanding: false,
          //     options: ['事假', '调休','病假','年假','产假','陪产假','婚假'],
          //     abstract: false
          //   },
          // },
          {
            title: '请假类型',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [
                { name: '事假', value: '1' },
                { name: '调休', value: '2' },
                { name: '病假', value: '3' },
                { name: '年假', value: '4' },
                { name: '产假', value: '5' },
                { name: '陪产假', value: '6' },
                { name: '婚假', value: '7' },
                { name: '例假', value: '8' },
                { name: '丧假', value: '9' },
                { name: '哺乳假', value: '10' },
              ], //固定项
              http: {},
              abstract: false
            }
          },
          // {
          //   title: '日期时间范围',
          //   name: 'DateCustomRange',
          //   icon: 'iconfont icon-kaoqin',
          //   valueType: ValueType.dateRange,
          //   props: {
          //     required: true,
          //     enablePrint: true,
          //     placeholder: ['开始时间', '结束时间'],
          //     format: 'YYYY-MM-DD HH:mm',
          //     showLength: true,
          //     abstract: false
          //   },
          // },
          {
            title: '开始时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '结束时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '时长',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              required: true,
              enablePrint: true,
              precision: undefined,
              abstract: false
            },
          },
        ],
      },
      {
        title: '补卡',
        icon: 'el-icon-calendar',
        itemType:'bizNode',
        name:'',
        props:{},
        child:[
          {
            title: '补卡时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
        ],
      },
      {
        title: '加班',
        icon: 'el-icon-more',
        itemType:'overTime',
        name:'',
        props:{},
        child:[
          {
            title: '加班类型',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '加班人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '开始时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '结束时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '时长',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              placeholder:'请输入时长',
              required: true,
              enablePrint: true,
              precision: undefined,
              abstract: false
            },
          },
        ],
      },
      {
        title: '外出',
        icon: 'el-icon-suitcase',
        itemType:'goOut',
        name:'',
        props:{},
        child:[
          {
            title: '外出类型',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '开始时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '结束时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '时长',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              required: true,
              enablePrint: true,
              precision: undefined,
              abstract: false
            },
          },
        ],
      },
      {
        title: '出差',
        icon: 'el-icon-position',
        itemType:'businessTrip',
        name:'',
        props:{},
        child:[
          {
            title: '出差事由',
            name: 'TextareaInput',
            icon: 'el-icon-more',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '交通工具',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '单程往返',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '出发城市',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '目的城市',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '开始时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '结束时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '时长',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              required: true,
              enablePrint: true,
              precision: undefined,
              abstract: false
            },
          },
          {
            title: '出差天数',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              isShow:true,
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '出差备注',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '出行人（同行人）',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '外部人员',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              isShow:true,
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
        ],
      },
      {
        title: '换班',
        icon: 'el-icon-operation',
        itemType:'shiftChange',
        name:'',
        props:{},
        child:[
          {
            title: '申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '替换人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '换班时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '还班时间',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD HH:mm',
              abstract: false
            },
          },
          {
            title: '时长',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              required: true,
              enablePrint: true,
              precision: undefined,
              abstract: false
            },
          },
        ],
      },
    ],
  },
  {
    name: '人事管理',
    components: [
      {
        title: '转正套件',
        icon: 'el-icon-wallet',
        itemType:'regularEmp',
        name:'',
        props:{},
        child:[
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '入职日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '试用期',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '转正日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '职位',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '岗位职级',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
        ],
      },
      {
        title: '离职套件',
        icon: 'el-icon-wallet',
        itemType:'resignation',
        name:'',
        props:{},
        child:[
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '入职日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '最后工作日',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '离职原因',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '离职原因备注',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
        ],
      },
      {
        title: '离职和交接套件',
        icon: 'el-icon-wallet',
        itemType:'resignationHandover',
        name:'',
        props:{},
        child:[
          {
            title: '入职日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '最后工作日',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '离职原因',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '离职原因备注',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '工作交接人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: false,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '工作交接事项',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
        ],
      },
      {
        title: '离职交接套件',
        icon: 'el-icon-wallet',
        itemType:'handover',
        name:'',
        props:{},
        child:[
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '最后工作日',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '工作交接人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              required: false,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
        ],
      },
      {
        title: '调岗套件',
        icon: 'el-icon-wallet',
        itemType:'changePost',
        name:'',
        props:{},
        child:[
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '入职日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '原部门',
            name: 'DeptPicker',
            icon: 'iconfont icon-map-site',
            value: [],
            valueType: ValueType.dept,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '原主部门',
            name: 'DeptPicker',
            icon: 'iconfont icon-map-site',
            value: [],
            valueType: ValueType.dept,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '原职位',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '原岗位职级',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '转入部门',
            name: 'DeptPicker',
            icon: 'iconfont icon-map-site',
            value: [],
            valueType: ValueType.dept,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '转入职位',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '新岗位职级',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '生效日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
        ],
      },
      {
        title: '入职套件',
        icon: 'el-icon-wallet',
        itemType:'entry',
        name:'',
        props:{},
        child:[
          {
            title: '入职员工姓名',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '用人部门',
            name: 'DeptPicker',
            icon: 'iconfont icon-map-site',
            value: [],
            valueType: ValueType.dept,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '职位',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '手机号',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '员工类型',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '预计入职日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
        ],
      },
      {
        title: '批量付款',
        icon: 'el-icon-wallet',
        itemType:'payBatch',
        name:'',
        props:{},
        child:[
          {
            title: '上传Excel文件',
            name: 'FileUpload',
            icon: 'el-icon-folderopened',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              onlyRead: false, //是否只读，false只能在线预览，true可以下载
              maxSize: 100, //文件最大大小MB
              maxNumber: 10, //最大上传数量
              fileTypes: [],
              abstract: false
            },
          },
          {
            title: '总金额（元）',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: false,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '总笔数',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: false,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '转账备注',
            name: 'TextareaInput',
            icon: 'el-icon-more',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: false,
              enablePrint: true,
              abstract: false
            },
          },
        ],
      },
      {
        title: '晋升套件',
        icon: 'el-icon-wallet',
        itemType:'promotion',
        name:'',
        props:{},
        child:[
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '入职日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
          {
            title: '部门',
            name: 'DeptPicker',
            icon: 'iconfont icon-map-site',
            value: [],
            valueType: ValueType.dept,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '原职位',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '新职位',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '原岗位职级',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '新岗位职级',
            name: 'SelectPlus',
            icon: 'el-icon-pointer',
            value: [],
            valueType: ValueType.array,
            props: {
              required: true,
              enablePrint: true,
              multiple: false, //是否多选
              expanding: false, //是否展开选项
              fixed: true, //是否是手动设置的固定项
              options: [{ name: '', value: '' }], //固定项
              http: {},
              abstract: false
            }
          },
          {
            title: '生效日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
        ],
      },
      {
        title: '人事综合套件',
        icon: 'el-icon-wallet',
        itemType:'comprehensive',
        name:'',
        props:{},
        child:[
          {
            title: '实际申请人',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              isShow:true,
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '姓名',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '生效日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              abstract: false
            },
          },
        ],
      },
    ],
  },
  {
    name: '扣款管理',
    components: [
      {
        title: '扣款套件',
        icon: 'el-icon-clock',
        itemType:'ticketDeduction',
        name:'',
        props:{},
        child:[
          {
            title: '日期',
            name: 'DateTime',
            icon: 'el-icon-calendar',
            value: '',
            valueType: ValueType.date,
            props: {
              required: true,
              enablePrint: true,
              format: 'YYYY-MM-DD',
              placeholder:'请选择日期',
              abstract: false
            },
          },
          {
            title: '违规人员部门',
            name: 'DeptPicker',
            icon: 'iconfont icon-map-site',
            value: [],
            valueType: ValueType.dept,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '违规人员',
            name: 'UserPicker',
            icon: 'el-icon-avatar',
            value: [],
            valueType: ValueType.user,
            props: {
              required: true,
              enablePrint: true,
              multiple: false,
              expansion: false,
              options: [],
              abstract: false
            },
          },
          {
            title: '扣款/扣分原因',
            name: 'TextInput',
            icon: 'el-icon-edit',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: true,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '扣款原因数据依据',
            name: 'ImageUpload',
            icon: 'el-icon-picture',
            value: [],
            valueType: ValueType.array,
            props: {
              required: false,
              enablePrint: true,
              maxSize: 5, //图片最大大小MB
              maxNumber: 10, //最大上传数量
              enableZip: true,
              abstract: false
            },
          },
          {
            title: '扣款分值',
            name: 'AmountInput',
            icon: 'iconfont icon-zhufangbutiezhanghu',
            value: '',
            valueType: ValueType.number,
            props: {
              placeholder:'请输入数值',
              required: false,
              enablePrint: true,
              showChinese: false,
              precision: undefined,
              abstract: false
            },
          },
          {
            title: '扣款金额',
            name: 'AmountInput',
            icon: 'iconfont icon-zhufangbutiezhanghu',
            value: '',
            valueType: ValueType.number,
            props: {
              required: false,
              enablePrint: true,
              showChinese: true,
              precision: undefined,
              abstract: false
            },
          },
          {
            title: '备注',
            name: 'TextareaInput',
            icon: 'el-icon-more',
            value: '',
            valueType: ValueType.string,
            props: {
              enableScan: false,
              required: false,
              enablePrint: true,
              abstract: false
            },
          },
          {
            title: '图片',
            name: 'ImageUpload',
            icon: 'el-icon-picture',
            value: [],
            valueType: ValueType.array,
            props: {
              required: false,
              enablePrint: true,
              maxSize: 5, //图片最大大小MB
              maxNumber: 10, //最大上传数量
              enableZip: true,
              abstract: false
            },
          },
          {
            title: '附件',
            name: 'FileUpload',
            icon: 'el-icon-folderopened',
            value: [],
            valueType: ValueType.array,
            props: {
              required: false,
              enablePrint: true,
              onlyRead: false, //是否只读，false只能在线预览，true可以下载
              maxSize: 100, //文件最大大小MB
              maxNumber: 10, //最大上传数量
              fileTypes: [],
              abstract: false
            },
          },
        ],
      },
    ],
  },
]

export default {
  groupComponents,
}
