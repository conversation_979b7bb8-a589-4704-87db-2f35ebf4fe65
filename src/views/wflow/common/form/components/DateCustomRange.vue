<template>
  <div>
    <div v-if="mode === 'DESIGN'">
      <el-date-picker style="width: 93%;" disabled :type="type" :start-placeholder="placeholder[0]"
        :end-placeholder="placeholder[1]" />
      <div class="length">
        <p class="p-class p-design"><span>*</span>时长（小时）：</p>
        <el-input disabled placeholder="请输入时长" v-model="timeLengthValue" />
        <!-- <span>{{ timeLength() }}</span> -->
      </div>
    </div>
    <div v-else-if="mode === 'PC' && !readonly">
      <el-date-picker v-model="_value" clearable :value-format="format" :format="format" :type="type"
        :start-placeholder="placeholder[0]" :end-placeholder="placeholder[1]" @change="handleChange" />
      <div class="length">
        <p class="p-class p-form"><span>*</span>时长（小时）：</p>
        <el-input disabled placeholder="请输入时长" v-model="timeLengthValue" />
        <!-- <span>{{ timeLength() }}</span> -->
      </div>
    </div>
    <div v-else-if="mode === 'MOBILE' && !readonly">
      <field right-icon="arrow" readonly clickable name="datetimePicker" v-model="_value[0]"
        :placeholder="placeholder[0]" @click="loadTime(0)" />
      <field right-icon="arrow" readonly clickable name="datetimePicker" v-model="_value[1]"
        :placeholder="placeholder[1]" @click="loadTime(1)" />
      <div class="length">
        <p class="p-class"><span>*</span>时长：</p>
        <el-input disabled placeholder="请输入时长" v-model="timeLengthValue" />
        <!-- <span>{{ timeLength() }}</span> -->
      </div>
      <popup v-model:show="showPicker" position="bottom">
        <picker-group :title="desc" :tabs="['选择日期', '选择时间']" @confirm="onConfirm">
          <date-picker :formatter="formatter" v-model="currentDate" />
          <time-picker v-if="format.indexOf('HH') > 0" :formatter="formatter" v-model="currentTime" />
        </picker-group>
      </popup>
    </div>
    <div v-else>
      {{ _value[0] }} ~ {{ _value[1] }}
      <div class="length">
        <p class="p-class p-description">时长（小时）</p>
        <!-- <el-input disabled placeholder="请输入时长" v-model="timeLengthValue" /> -->
        <span>{{ timeLength() }}</span>
      </div>
    </div>

  </div>
</template>

<script>
import { DatePicker, Field, PickerGroup, Popup, showFailToast, TimePicker } from 'vant'
import componentMinxins from '../ComponentMinxins'
import moment from 'moment'

export default {
  mixins: [componentMinxins],
  name: 'DateTimeRange',
  components: { DatePicker, TimePicker, PickerGroup, Field, Popup },
  props: {
    modelValue: {
      type: Array,
      default: () => {
        return []
      },
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD HH:mm',
    },
    placeholder: {
      type: Array,
      default: () => {
        return ['开始时间', '结束时间']
      },
    },
    showLength: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    type() {
      switch (this.format) {
        case 'YYYY-MM-DD':
          return 'daterange'
        case 'YYYY-MM-DD HH:mm':
          return 'datetimerange'
        default:
          return 'daterange'
      }
    },
    desc() {
      switch (this.format) {
        case 'YYYY':
          return '选择年份'
        case 'YYYY-MM':
          return '选择年-月'
        case 'YYYY-MM-DD':
          return '选择年-月-日'
        case 'YYYY-MM-DD HH:mm':
          return '选择年-月-日 时:分'
        default:
          return '选择年-月-日 时:分'
      }
    },
  },
  data() {
    return {
      showPicker: false,
      valIndex: 0,
      currentDate: [],
      currentTime: [],
      timeVal: [new Date(), new Date()],
      timeLengthValue: ''
    }
  },
  methods: {
    loadTime(index) {
      this.showPicker = true
      this.valIndex = index
      if (this.$isNotEmpty(this.modelValue[index])) {
        const time = this.modelValue[index].split(' ')
        this.currentDate = time[0].split('-')
        this.currentTime = time[1].split(':')
      } else {
        this.currentDate = []
        this.currentTime = []
      }
    },
    getFormatVal(val) {
      return `${val[0].selectedValues.join('-')} ${val[1].selectedValues.join(':')}`
    },
    timeLength() {
      if (Array.isArray(this.modelValue) && this.modelValue.length > 0) {
        const time1 = this.modelValue[0]
        const time2 = this.modelValue[1]
        const date1 = new Date(time1.replace(/-/g, '/'))
        const date2 = new Date(time2.replace(/-/g, '/'))
        const differenceInMilliseconds = date2 - date1
        const differenceInHours = differenceInMilliseconds / (1000 * 60 * 60)
        return differenceInHours.toFixed(2)
      } else {
        return ''
      }
    },
    handleChange() {
      if (Array.isArray(this.modelValue) && this.modelValue.length > 0) {
        const time1 = this.modelValue[0]
        const time2 = this.modelValue[1]
        const date1 = new Date(time1.replace(/-/g, '/'))
        const date2 = new Date(time2.replace(/-/g, '/'))
        const differenceInMilliseconds = date2 - date1
        const differenceInHours = differenceInMilliseconds / (1000 * 60 * 60)
        this.timeLengthValue = differenceInHours.toFixed(2)
      } else {
        this.timeLengthValue = ''
      }
    },
    onConfirm(val) {
      this.showPicker = false
      this._value[this.valIndex] = this.getFormatVal(val)
      if (this._value[0] && this._value[1]) {
        if (!moment(this._value[0]).isBefore(moment(this._value[1]))) {
          this._value[this.valIndex] = undefined
          showFailToast('开始时间必须小于结束时间')
        } else {
          this.$emit('update:modelValue', this._value)
        }
      }
    },
    formatter(type, option) {
      switch (type) {
        case 'year': option.text += ' 年'; break;
        case 'month': option.text += ' 月'; break;
        case 'day': option.text += ' 日'; break;
        case 'hour': option.text += ' 时'; break;
        case 'minute': option.text += ' 分'; break;
      }
      return option
    },
  },
  emits: ['update:modelValue'],
}
</script>

<style scoped lang="less">
.length {
  margin-top: 5px;
}

.length:nth-child(2) {
  color: #8c8c8c;
}

:deep(.el-date-editor--datetimerange .el-input__inner) {
  width: 100%;
  max-width: 400px;
}

.p-class {
  margin: 10px 0 6px;
  line-height: normal;
  padding: 0;
  color: #747677;
  font-size: 13px;

  span {
    margin-right: 4px;
    color: #f56c6c;
  }
}

.p-form {
  margin: 18px 0 8px;
}

.p-design {
  span {
    margin-left: -8px;
  }
}

.p-description {
  margin: 24px 0px 1px;
  line-height: normal;
  line-height: 100% !important;
}
</style>
