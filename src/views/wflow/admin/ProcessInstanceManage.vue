<template>
  <div v-loading="loading" style="background: #fff; padding: 20px">
    <el-form :inline="true">
      <el-form-item label="企业">
        <el-select
          v-model="tenantId"
          placeholder="请选择企业"
          filterable
          clearable
          multiple
          class="!w-240px"
        >
          <el-option
            v-for="item in entList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型">
        <!-- <el-input placeholder="请输入类型" clearable class="!w-240px mr-10px" v-model="approvalType" @clear="handleChange" /> -->
        <el-select
          v-model="approvalTypes"
          placeholder="请选择类型"
          filterable
          clearable
          remote
          :remote-method="hanlderNameFilterChange"
          remote-show-suffix
          multiple
          :loading="nameFilterLoading"
          class="!w-240px"
        >
          <el-option
            v-for="(item,findex) in filterNameList"
            :key="item.formId"
            :label="item.tenantName ? item.tenantName + '_' + item.formName : item.formName"
            :value="item.formName"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发起人" prop="username">
        <el-input
          placeholder="请输入发起人"
          clearable
          class="!w-240px mr-10px"
          @keyup.enter="handleChange"
          v-model="startUser"
          @clear="handleChange"
        />
      </el-form-item>

      <el-form-item>
        <el-select
          v-model="params.status"
          placeholder="请选择审批状态"
          filterable
          clearable
          class="!w-240px mr-10px"
        >
          <el-option
            v-for="item in approvalList"
            :key="item.id"
            :label="item.name"
            :value="item.name"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="提交时间" prop="createTime">
        <el-date-picker
          v-model="startTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
          @change="selectStart"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="createTime">
        <el-date-picker
          v-model="endTime"
          value-format="YYYY-MM-DD"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="!w-240px"
          @change="selectEnter"
        />
      </el-form-item>
      <el-form-item label="内容检索" prop="createTime">
        <el-input
          placeholder="请输入"
          clearable
          class="!w-240px mr-10px"
          @keyup.enter="handleChange"
          v-model="content"
          @clear="handleChange"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleChange" icon="el-icon-search">搜索</el-button>
        <el-button
          @click="handleDown"
          icon="el-icon-download"
          type="success"
          plain
          v-loading.fullscreen.lock="loadingAll"
          >导出
        </el-button>
        <el-button
          v-hasPermi="['wflow:instance:delete']"
          @click="handleDelete()"
          icon="el-icon-delete"
          type="danger"
          plain
          v-loading.fullscreen.lock="loadingAll"
          >批量删除
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      @selection-change="handleSelectionChange"
      :data="dataList"
      :header-cell-style="{ background: '#e8e8e8' }"
      style="width: 100%; margin: 0 0 20px"
      @row-click="showProcess"
      @sort-change="handleSortChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column
        fixed
        prop="processDefName"
        label="审批类型"
        show-overflow-tooltip
        min-width="120px"
      >
        <template v-slot="scope">
          <el-tag
            size="small"
            type="success"
            v-if="scope.row.superInstanceId !== scope.row.instanceId"
            >子
          </el-tag>
          <span style="margin-left: 5px">{{ scope.row.processDefName }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed prop="content" label="摘要" min-width="240px">
        <template #default="scope">
          <el-tooltip placement="right" popper-class="custom-popper-class">
            <template #content>
              <div v-html="tooltipContent(scope.row.content)" class="customTooltip"></div>
            </template>
            <div class="contentScope">{{ scope.row.content }}</div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="staterUser" show-overflow-tooltip label="发起人" min-width="100px">
        <template v-slot="scope">
          <avatar
            :size="26"
            :name="scope.row.staterUser ? scope.row.staterUser.name : 'name'"
            :src="scope.row.staterUser ? scope.row.staterUser.avatar : ''"
            class="custom-avatar"
          />
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="tenantName" label="公司名称" min-width="140px" />
      <el-table-column
        show-overflow-tooltip
        prop="startTime"
        label="提交时间"
        min-width="110px"
        sortable="custom"
      />
      <el-table-column
        show-overflow-tooltip
        prop="finishTime"
        label="结束时间"
        min-width="110px"
        sortable="custom"
      />
      <el-table-column show-overflow-tooltip prop="taskName" label="当前节点" />
      <el-table-column prop="status" label="审批状态">
        <template v-slot="scope">
          <el-tag :type="getProcTag(scope.row.result)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        show-overflow-tooltip
        prop="duration"
        label="已耗时"
        min-width="120px"
        sortable="custom"
      >
        <template v-slot="scope">
          {{ scope.row.execTime ? scope.row.execTime : getDuration(scope.row) }}
        </template>
      </el-table-column>
      <!--      <el-table-column fixed="right" label="操作" min-width="90">
          <template slot-scope="scope">
            <el-button type="text">撤回</el-button>
          </template>
        </el-table-column>-->
    </el-table>
    <div style="text-align: right">
      <el-pagination
        background
        :page-sizes="[10, 20, 30, 50, 100]"
        layout="total, sizes,prev, pager, next,jumper"
        :total="total"
        :page-size="params.pageSize"
        v-model:current-page="params.pageNo"
        @size-change="handleSizeChange"
      />
    </div>
    <el-drawer
      :size="isMobile ? '100%' : '560px'"
      direction="rtl"
      title="审批详情"
      :z-index="1000"
      v-model="processVisible"
      class="custom-detail-header"
      @close="handleClose"
    >
      <instance-preview
        v-if="processVisible"
        :instance-id="selectInstance"
        @handler-after="handlerAfter"
      />
    </el-drawer>
  </div>
</template>

<script>
import taskApi from '@/api/processTask'
import InstancePreview from '../workspace/approval/ProcessInstancePreview.vue'
import { getProcTag } from '@/utils/ProcessUtil.js'
import download from '@/utils/download'
import moment from "moment"
import { RegisterApi } from '@/api/login/register';

export default {
  name: 'ProcessInstancePreview',
  components: { InstancePreview },
  data() {
    return {
      total: 0,
      params: {
        pageSize: 10,
        pageNo: 1,
        status: '',
        sortKey: '',
        sortType: ''
      },
      selectInstance: '',
      loading: false,
      processVisible: false,
      formList: [],
      dataList: [],
      startUser: '',
      content: '',
      startTime: [],
      endTime: [],
      loadingAll: false,
      typeList: [],
      approvalList: [
        {
          name: '审批通过',
          id: 1
        },
        {
          name: '审批进行中',
          id: 2
        },
        {
          name: '审批被驳回',
          id: 3
        },
        {
          name: '审批被撤销',
          id: 4
        }
      ],
      selectedList: [],
      rowStatus: '',
      approvalTypes: [],
      filterNameList:[],

      nameFilterLoading:false,
      tenantId: [],
      // 企业列表
      entList: [],
      tenantNameFilterLoading:false,
    }
  },
  computed: {
    isMobile() {
      return window.screen.width < 450
    }
  },
  watch: {
    params: {
      deep: true,
      handler() {
        this.getSubmittedList()
      }
    }
  },
  mounted() {
    this.startTime = [moment().subtract(30, 'days').format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
    this.reqEntList()
    this.getGroupModelList()
    this.getSubmittedList()
  },
  methods: {
    hanlderNameFilterChange(key){
      if(key){
        this.nameFilterLoading = true
        setTimeout(() => {
          this.nameFilterLoading = false
          this.filterNameList = this.typeList.filter(e=>{
            return e.formName.includes(key)
          })
        }, 100);
      }else{
        this.filterNameList = this.typeList
      }
    },
    handleClose() {
      if (this.rowStatus === '审批进行中') {
        this.handlerAfter()
      } else {
        this.processVisible = false
      }
    },
    tooltipContent(content) {
      return content.replace(/\n/g, '<br/>')
    },
    //  类型下拉
    getGroupModelList() {
      taskApi.groupModelList().then((res) => {
        this.typeList = res.data
      })
    },
    // 请求企业列表数据
    reqEntList (){
      RegisterApi.getTenantList().then(res => {
        this.entList = res
      })
    },
    handleDown() {
      // console.log(this.startTime)
      //加类型限制，以防导出数据过大，卡死
      if (!this.approvalTypes || this.approvalTypes.length === 0) {
        return this.$message.warning('请选择流程类型')
      }
      // if (this.params.status && this.params.status != '审批通过') {
      //   return this.$message.warning('请选择【审批通过】状态下的数据')
      // }
      this.loadingAll = true
      let query = {
        ...this.params,
        approvalTypes: this.approvalTypes.join(';'),
        startUser: this.startUser,
        content: this.content,
        startTime: this.startTime && this.startTime.length > 0 ? this.startTime.join(',') : '',
        endTime: this.endTime && this.endTime.length > 0 ? this.endTime.join(',') : ''
      }
      taskApi
        .processExport(query)
        .then((data) => {
          this.loadingAll = false
          download.excel(
            data,
            (this.approvalTypes && this.approvalTypes.length > 0 ? this.approvalTypes.join('、') : '审批流程') + '_导出数据.xlsx'
          )
        })
        .catch((e) => {
          this.loadingAll = false
        })
    },
    selectEnter(v) {
      // if (!v) {
      this.getSubmittedList()
      // }
    },
    selectStart(v) {
      // if (!v) {
      this.getSubmittedList()
      // }
    },
    handleChange() {
      if (this.params.pageNo == 1) {
        this.getSubmittedList()
      } else {
        this.params.pageNo = 1
      }
    },
    handleSizeChange(val) {
      this.params.pageSize = val
    },
    getProcTag,
    getSubmittedList() {
      this.loading = true
      let query = {
        ...this.params,
        tenantId: this.tenantId.join(';'),
        approvalTypes: this.approvalTypes.join(';'),
        startUser: this.startUser,
        content: this.content,
        startTime: this.startTime && this.startTime.length > 0 ? this.startTime.join(',') : '',
        endTime: this.endTime && this.endTime.length > 0 ? this.endTime.join(',') : ''
      }
      taskApi
        .getSubmittedList(query)
        .then((rsp) => {
          this.loading = false
          this.total = rsp.data.total
          this.dataList = rsp.data.records
        })
        .catch((e) => {
          this.loading = false
        })
    },
    showProcess(row) {
      this.processVisible = true
      this.selectInstance = row.instanceId
      this.rowStatus = row.status
    },
    getDuration(row) {
      const startDate = new Date(row.startTime)
      const endDate = row.finishTime ? new Date(row.finishTime) : new Date()
      const timeDifference = endDate - startDate

      const seconds = Math.max(Math.floor((timeDifference / 1000) % 60), 0)
      const minutes = Math.floor((timeDifference / 1000 / 60) % 60)
      const hours = Math.floor((timeDifference / (1000 * 60 * 60)) % 24)
      const days = Math.floor(timeDifference / (1000 * 60 * 60 * 24))

      const parts = []
      if (days > 0) parts.push(`${days}天`)
      if (hours > 0) parts.push(`${hours}小时`)
      if (minutes > 0) parts.push(`${minutes}分钟`)
      if (seconds > 0 || parts.length === 0) parts.push(`${seconds}秒`)
      // if (minutes > 0 || parts.length === 0) parts.push(`${minutes}分钟`)
      return parts.join('')
    },
    handlerAfter() {
      this.processVisible = false
      this.getSubmittedList()
    },
    handleDelete() {
      if (this.selectedList.length == 0) {
        return this.$message.warning('请勾选需要删除的内容')
      }

      let ids = this.selectedList.map((item) => item.instanceId)

      this.$confirm('是否确认批量删除已选择信息?', '批量删除', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(() => {
        taskApi.instanceBatchDelDelete(ids).then((res) => {
          this.$message.success('删除成功')
          this.getSubmittedList()
        })
      })
    },
    handleSelectionChange(val) {
      console.log('handleSelectionChange=', val)
      this.selectedList = val
    },
    handleSortChange({ prop, order }) {
      if (order === 'ascending') {
        this.params.sortType = 'asc'
      } else if (order === 'descending') {
        this.params.sortType = 'desc'
      } else {
        this.params.sortType = ''
      }
      this.params.sortKey = prop
      this.getSubmittedList()
    }
  }
}
</script>

<style scoped lang="less">
.el-pagination {
  justify-content: right;
  margin-top: 20px;
}

.textTi {
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
}

/deep/ .textTi .el-select__placeholder.is-transparent {
  color: #333 !important;
}

:deep(.el-form-item) {
  margin-right: 25px;
}

.contentScope {
  white-space: pre-wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 5;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.custom-avatar .a-img > div) {
  font-size: 10px !important;
}
</style>
<style>
.customTooltip {
  min-width: 200px;
  max-width: 400px;
  white-space: pre-wrap;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 20;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-popper-class {
  background: #45484a;
}

.custom-popper-class .el-popper__arrow::before {
  border: 1px solid #45484a;
  background: #45484a;
}
</style>
