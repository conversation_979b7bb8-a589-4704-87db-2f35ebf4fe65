<template>
  <el-row class="rowBox relative" :class="{ isCol: isCollapse, 'isCBox-else': windowWidth < 768 }">
    <!-- 左侧部门树 -->
    <el-col :span="4" :xl="4" :lg="5" :md="6" :sm="7" :xs="8" class="left-col">
      <div v-for="(item, i) in orgobj" :key="i">
        <div class="left" @click="expandShrink(i)">
          <img :src="item.tenantLogo" alt="" />
          <div class="tetx" :title="item.tenantName">{{ item.tenantName }}</div>
          <el-icon v-show="item.flag">
            <CaretBottom />
          </el-icon>
          <el-icon v-show="!item.flag">
            <CaretTop />
          </el-icon>
        </div>
        <div
          v-show="!item.flag"
          v-for="(it, index) in item.leftListData"
          :key="index"
          class="leftBottom"
          :class="tableIndex == it.id + item.tenantId ? 'leftBottomBg' : ''"
          @click="queryUser(it.id, item.tenantId)"
        >
          <img src="../../../assets/imgs/rectangle.png" alt="" />
          <div class="tetx" :title="it.name">{{ it.name }}</div>
        </div>
      </div>
      <!-- <div class="left" @click="expandShrink">
          <img :src="userInfo.tenantLogo" alt="" />
          <div class="tetx" :title="userInfo.tenantName">{{ userInfo.tenantName }}</div>
          <el-icon v-show="flag">
            <CaretBottom />
          </el-icon>
          <el-icon v-show="!flag">
            <CaretTop />
          </el-icon>
        </div>
        <div
          v-show="!flag"
          v-for="(item, i) in leftListData"
          :key="i"
          class="leftBottom"
          :class="tableIndex == i ? 'leftBottomBg' : ''"
          @click="queryUser(item.id, i)"
        >
          <img src="../../../assets/imgs/rectangle.png" alt="" />
          <div class="tetx" :title="item.name">{{ item.name }}</div>
        </div> -->
    </el-col>
    <el-col :span="20" :xl="20" :lg="19" :md="18" :sm="17" :xs="16">
      <div class="right">
        <div class="rightTop">
          <div class="rightTopL">
            <img v-if="userInfo.tenantLogo" class="rightTopImg" :src="userInfo.tenantLogo" alt="" />
            <div class="text">
              <div>{{ userInfo.tenantName }}</div>
              <div class="textB">
                <!-- <span>
                    <img src="../../../assets/imgs/auti.png" alt="" />
                    中级认证</span
                  >
                  <span>标准版</span> -->
              </div>
            </div>
          </div>
          <!--  -->
          <!-- <div class="rightTopR">
              <img src="../../../assets/imgs/invite.png" alt="" />
              <img src="../../../assets/imgs/invite.png" alt="" />
            </div> -->
        </div>
        <!--  -->
        <div class="rightC">
          <div class="rightCT">
            <el-breadcrumb ref="fileBreadcrumb" separator=">">
              <el-breadcrumb-item v-for="(item, index) in TopListData" :key="index">
                <a
                  href="javascript:void(0);"
                  :title="item.name"
                  @click="breadcrumbClick(item.id, index)"
                  :class="{ disabled: index == TopListData.length - 1 }"
                  >{{ item.name }}</a
                >
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div
            class="rightBottom"
            v-if="tableIndex != '-1' && queryParams.id != 0"
            @click="handleAdd"
          >
            <div class="rightBottomFF">
              <img src="../../../assets/imgs/addUser.png" />
              <span style="margin-left: 12px">邀请成员加入</span>
            </div>
          </div>
          <div
            v-for="(item, i) in rightListData"
            @click="rightQuerydata(item)"
            :key="i"
            class="rightBottom"
          >
            <div class="rightBottomFF">
              <img src="../../../assets/imgs/orz.png" v-show="item.type == 'dept'" />
              <!-- <div class="userYuan" v-show="item.type != 'dept'">
                {{ item.name.length >= 3 ? item.name.substring(1, 3) : item.name }}
              </div> -->
              <avatar :size="36" :name="item.name" :src="item.avatar" class="userYuan" v-show="item.type != 'dept'"/>
              <span style="margin-left: 12px">{{ item.name }} </span>
              <span
                v-show="item.type == 'dept'"
                style="margin-left: 5px; color: #a2a3a4; font-size: 12px"
                >（{{ item.employNum + '人' }}）
              </span>
            </div>
            <el-icon v-show="item.type == 'dept'">
              <ArrowRight />
            </el-icon>
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
  <!-- 个人信息弹窗 -->
  <UserDialog ref="userRef"></UserDialog>
  <!-- 邀请人员 -->
  <addDrawer :id="queryParams.id" :info="userInfo" ref="addDrawerRef"></addDrawer>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as RoleApi from '@/api/system/role'
import { getUserProfile, getListProfile } from '@/api/system/user/profile'
import { CaretTop, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import addDrawer from './addUser.vue'
import UserDialog from './userDialog.vue'
defineOptions({ name: 'orgstructure' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const showFilePath = [
  // 面包屑显示数组
  { id: 0, label: '111' },
  { id: 1, label: '222' },
  { id: 2, label: '333' },
  { id: 3, label: '444' }
]
const images = [
  {
    id: '1',
    img: new URL('@/assets/imgs/ic1.png', import.meta.url)
  },
  {
    id: '2',
    img: new URL('@/assets/imgs/ic2.png', import.meta.url)
  },
  {
    id: '3',
    img: new URL('@/assets/imgs/ic3.png', import.meta.url)
  },
  {
    id: '4',
    img: new URL('@/assets/imgs/ic4.png', import.meta.url)
  }
]
const queryParams = reactive({
  id: '',
  tenantId: ''
})
const orgParams = reactive({
  type: 'user',
  deptId: '',
  tenantId: ''
})
const tableIndex = ref('-1')

const leftListData = ref([
  {
    name: '组织架构',
    id: '0'
  }
  // {
  //   name: 'AI助理',
  //   id: '1'
  // },
  // {
  //   name: '我的客户',
  //   id: '2'
  // }
])

const orgobj = reactive([])

const rightListData = ref([])
const TopListData = ref([])
const userInfo = ref({})
const userInfoList = ref([])
const flag = ref(false)
/** 查询面包屑导航栏 */
const getParentList = async (val, i) => {
  loading.value = true
  console.log(i, 'i能输出几次')

  try {
    const data = await RoleApi.getParentList(queryParams)
    console.log(data, 'hahahahahahha')
    if (val != 1) {
      const newV = data.slice(1).reduce((accumulator, currentValue, index, array) => {
        if (index !== array.length - 1) {
          return accumulator + currentValue.name + '-'
        } else {
          // 否则，只返回当前元素的名字
          return accumulator + currentValue.name
        }
      }, '')
      const obj = {
        name: newV,
        id: queryParams.id
      }
      // leftListData.value.splice(1, 0, obj)
      // orgobj.value[i].leftListData.push(obj)
      orgobj[i].leftListData.push(obj)

      console.log(leftListData.value, '最钟结果')

      console.log(orgobj)
    } else {
      console.log('这个判断进入几次')
      console.log(val, 'sadasdsadasdasdsa')
      console.log(i, 'dasadad')
      TopListData.value = []
      TopListData.value = data
      console.log(TopListData.value, 'TopListData.value')
    }
  } finally {
    loading.value = false
  }
}
const userRef = ref()
// 点击右侧展示的部门获取人员信息
const rightQuerydata = async (val) => {
  loading.value = true
  try {
    // console.log(val)
    queryParams.id = val.id
    orgParams.deptId = val.id
    if (val.type == 'dept') {
      getOrgtree()
    } else {
      userRef.value.open(val.id, queryParams.tenantId)
    }
  } finally {
    loading.value = false
  }
}

// 获取用户个人登录信息
const getUserInfo = async () => {
  const users = await getListProfile()
  // const users = await getUserProfile()
  userInfoList.value = users
  // console.log(userInfoList.value.length,'adsa');
  
  //  queryUser(0, users[0].tenantId)
  //  return
  for (let i = 0; i < users.length; i++) {
    orgobj.push({
      tenantName: users[i].tenantName,
      tenantLogo: users[i].tenantLogo,
      tenantId: users[i].tenantId,
      flag:userInfoList.value.length<=1? false : true,
      leftListData: [
        {
          name: '组织架构',
          id: '0'
        }
      ]
    })
    for (let j = 0; j < users[i].dept.length; j++) {
      queryParams.id = users[i].dept[j].id
      queryParams.tenantId = users[i].tenantId
      await getParentList('', i)
     
      if( userInfoList.value.length<=1){
  if (i == 0) {
        //新进入当前页面时默认选中第一个
        console.log('执行')
        await queryUser(0, users[i].tenantId)
      }
      }
    
    }
  }

  return
  userInfo.value = users
  // console.log(users)
  // queryParams.id = users.dept.id
  // getParentList('')
  for (let i = 0; i < users.dept.length; i++) {
    queryParams.id = users.dept[i].id
    await getParentList('')
    await queryUser(0, 0)
  }
}

const expandShrink = (i) => {
  console.log(i)
  orgobj[i].flag = !orgobj[i].flag
}
// 点击面包屑导航
const breadcrumbClick = (id, index) => {
  if (index != TopListData.value.length - 1) {
    if (index == 0) {
      orgParams.deptId = '0'
      queryParams.id = '0'
    } else {
      orgParams.deptId = id
      queryParams.id = id
    }

    getOrgtree()
  }
}
// 获取右侧人员信息
const getOrgtree = async () => {
  try {
    const data = await RoleApi.getOrgtree(orgParams)
    rightListData.value = data
    getParentList(1, '')
  } finally {
    loading.value = false
  }
}
// 点击左侧部门
const queryUser = async (id, tenantId) => {
  console.log(id)
  console.log('执行的时间')

  try {
    userInfoList.value.map((item) => {
      if (item.tenantId == tenantId) {
        userInfo.value = item
      }
    })
    tableIndex.value = id + tenantId
    orgParams.deptId = id
    orgParams.tenantId = tenantId
    queryParams.id = id
    queryParams.tenantId = tenantId
    getOrgtree()
  } finally {
    loading.value = false
  }
}
// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}
// 邀请成员
const addDrawerRef = ref()
const handleAdd = async () => {
  addDrawerRef.value.open(queryParams.tenantId)
}

/** 初始化 */
onMounted(() => {
  getUserInfo()
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.left {
  display: flex;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  margin: 8px 8px;

  img {
    width: 30px;
    height: 30px;
    border-radius: 5px;
  }

  .el-icon {
    color: #a2a3a4;
  }

  .tetx {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 10px;
    margin-right: 4px;
    font-size: 14px;
    color: #301333;
  }
}

.left:hover {
  background-color: #f3f4f7;
}

.leftBottom {
  display: flex;
  align-items: center;
  padding: 12px 8px 12px 19px;
  border-radius: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  margin: 8px 8px;

  .tetx {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 20px;
    font-size: 14px;
  }
}

.leftBottom:hover {
  background-color: #f3f4f7;
}

.leftBottomBg {
  background-color: #f3f4f7;
}

.right {
  height: 100%;
  .rightTop {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 16px;
    padding: 16px 0;
    border-bottom: 1px solid #e9eced;
    color: #303133;

    .rightTopL {
      display: flex;
      align-items: center;

      .rightTopImg {
        width: 30px;
        height: 30px;
        border-radius: 5px;
      }

      .text {
        margin-left: 15px;

        // margin-bottom: 15px; //先注释后面需要放开
        .textB {
          color: #9da0a1;
          font-size: 14px;
          // margin-top: 8px;

          > span {
            border: 1px solid #ebedee;
            border-radius: 5px;
            padding: 5px;
            margin-right: 15px;
          }
        }
      }
    }

    .rightTopR {
      img {
        margin-right: 20px;
      }
    }
  }

  .xian {
    width: 100%;
    height: 1px;
    background: #eaeced;
    margin-bottom: 15px;
  }
}

.rightC {
  height: calc(100% - 80px);
  margin: 16px 0 0;
  overflow-y: auto;

  .rightCT {
    margin-bottom: 20px;
    padding: 4px 16px 0;
  }

  .rightBottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px;
    padding: 8px 6px;
    margin: 8px 10px;
    cursor: pointer;
    font-size: 14px;

    img {
      vertical-align: middle;
      width: 36px;
      height: 36px;
    }

    .rightBottomFF {
      display: flex;
      align-items: center;
    }

    .userYuan {
      width: 36px;
      height: 36px;
      // background: #3370ff;
      border-radius: 5px;
      text-align: center;
      line-height: 36px;
      color: #ffffff;
      box-sizing: border-box;
      font-size: 12px;
      overflow: hidden;
      :deep(.a-img>div) {
        font-size: 12px;
        font-weight: 500;
        background: #3370FF;
      }
    }
  }
}

.rightBottom:hover {
  background-color: #f3f4f7;
}

:deep.disabled {
  cursor: not-allowed !important;
}

.el-card__body {
  padding: 0 !important;
}

.container {
  position: relative;
  width: 240px;

  .big-image {
    width: 100%;
    height: 100%;
  }

  .small-image {
    position: absolute;
    top: 12px;
    right: 12px;
  }
}

.detailsleft {
  width: 240px;
  height: 320px;
  background: #ffffff;
  border-radius: 10px;
  position: relative;
  top: -10px;

  .nameT {
    width: 118px;
    height: 118px;
    border-radius: 10px;
    background: #007fff;
    color: #ffffff;
    text-align: center;
    line-height: 118px;
    font-size: 40px;
    font-weight: 500;
    position: absolute;
    top: -50px;
    left: 18px;
    border: 2px solid #ffffff;
  }

  .aniu {
    position: absolute;
    right: 12px;
    top: 12px;
  }

  .aniu1 {
    width: 240px;
    position: absolute;
    bottom: 12px;
    text-align: center;
  }

  .frie {
    background: #eaf0ff;
    color: #007fff;
    border-color: #eaf0ff;

    img {
      margin-right: 3px;
    }
  }

  .cter {
    position: absolute;
    top: 84px;
    left: 16px;

    .nam {
      font-weight: 500;
      font-size: 20px;
      color: #171a1d;
    }

    .ente {
      font-weight: 400;
      font-size: 14px;
      color: #737677;
      margin-top: 8px;
    }

    img {
      margin-right: 18px;
      margin-top: 14px;
    }
  }
}

.detailsR {
  width: 276px;
  // height: 216px;
  background: #ffffff;
  border-radius: 8px;
  margin-top: 9px;
}

.detailsRText {
  padding: 16px 0 5px 12px;
  display: flex;

  .detailsRLeft {
    flex: 0.4;
    font-weight: 400;
    font-size: 14px;
    color: #737677;
  }

  .detailsRLeft1 {
    flex: 1;
    font-weight: 400;
    font-size: 14px;
    color: #171a1d;
  }

  .detailsRLeft2 {
    font-weight: 400;
    font-size: 14px;
    color: #171a1d;
    flex: 0.56;
    margin-right: 40px;
  }

  .textColor {
    color: #007fff;
  }
}

.detailsRB {
  width: 276px;
  height: 174px;
  background: #ffffff;
  border-radius: 8px;
  margin-top: 10px;
}

.rowBox {
  margin: 0 0 0 20px;
  position: fixed;
  left: 180px;
  right: 15px;
  top: 81px;
  bottom: 0;
  background: #fff;
}

.el-col {
  height: 100%;
}

.left-col {
  padding: 0 !important;
  border-right: 1px solid #eceded;
  box-sizing: border-box;
}

.el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
  color: #3370ff;
  font-weight: normal;
}

.el-breadcrumb__separator {
  color: #a2a3a4;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: #a2a3a4;
}

.isCol {
  left: 64px;
  margin-left: 0 !important;
}

.isCBox-else {
  left: 0;
}

.el-col {
  height: 100%;
  // overflow: auto;
}
</style>
