<template>
  <ContentWrap v-if="flag">
    <el-button type="primary" @click="addAttendanceGroup" class="mBT">新增考勤组</el-button>
    <el-table v-loading="loading" :data="Bglist">
      <el-table-column align="center" label="名称" prop="name" />
      <el-table-column align="center" label="人数" prop="userCount" />
      <el-table-column align="center" label="类型" prop="typeDesc" />
      <el-table-column align="center" label="考勤时间" prop="timeDesc" min-width="500px">
        <template #default="scope">
          <div v-for="(item, i) in scope.row.timeDesc" :key="i">
            {{ item }}
          </div>
        </template>
      </el-table-column>
      <el-table-column :width="300" label="操作">
        <template #default="scope">
          <el-button link type="primary" @click="openForm(scope.row.id)"> 编辑 </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      v-model:limit="queryParams.pageSize"
      v-model:page="queryParams.pageNo"
      :total="total"
      @pagination="groupList"
    />
  </ContentWrap>
  <!--  -->
  <ContentWrap v-else>
    <div class="top">
      <el-icon style="vertical-align: middle; cursor: pointer" @click="goBack">
        <Back />
      </el-icon>
      {{ backTitle }}
    </div>
    <el-form ref="formRef" label-width="80px" label-position="top">
      <el-form-item label="考勤组名称">
        <el-input v-model="kaoQinName" style="width: 240px" placeholder="请输入考勤组名称" />
      </el-form-item>
      <el-form-item>
        <template #label>
          <span class="topSx">考勤人员</span>
          <el-button type="primary" link @click="setUpDra(1)">设置</el-button>
        </template>
        <div>
          <div>
            <span>参与考勤人员: </span>
            <span v-if="newUser.length > 0">
              <span v-for="(item, i) in newUser" :key="i"
                >{{ item.name }}{{ i === newUser.length - 1 ? '' : '、' }}</span
              >
            </span>
            <span v-else>未设置</span>
          </div>
          <div>
            <span>无需考勤人员: </span>
            <span v-if="newUser1.length > 0">
              <span v-for="(item, i) in newUser1" :key="i"
                >{{ item.name }}{{ i === newUser1.length - 1 ? '' : '、' }}</span
              >
            </span>
            <span v-else>未设置</span>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <template #label>
          <span class="topSx">考勤时间</span>
          <el-button type="primary" link @click="setUpDra(2)">设置</el-button>
        </template>
        <div>
          <div>
            <span>考勤类型: </span>
            <span>固定班制(固定时间上下班)</span>
          </div>
          <div>
            工作日设置:
            <span v-if="selectionData.length > 0">
              <span v-for="(item, i) in selectionData" :key="i">
                {{ item.week }}
              </span>
            </span>
            <span v-else>
              <span v-for="(item, i) in timeList" :key="i">
                <span v-if="i < timeList.length - 2">
                  {{ item.week + ' ' }}
                </span>
              </span>
            </span>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <template #label>
          <span class="topSx">打卡方式</span>
          <el-button type="primary" link @click="setUpDra(3)">设置</el-button>
        </template>
        <div>
          <div>
            <span>考勤方式: </span>
            <span>地点打卡</span>
          </div>
          <div>
            <span>地点: </span>
            <span v-if="!addressList || addressList.length == 0 || !addressList[0].location">未设置</span>
            <span v-else>
              <span v-for="(item, i) in addressList" :key="i">
                {{ item.location }}
                <span v-if="i < addressList.length - 1">、</span>
              </span>
            </span>
          </div>
        </div>
      </el-form-item>

      <el-form-item>
        <template #label>
          <span class="topSx">拍照验证规则</span>
          <el-button type="primary" link @click="setUpDra(4)">设置</el-button>
        </template>
        <div>
          <div>
            <span>{{ photographFlag == 0 ? '已开启拍照打卡' : '未开启拍照打卡' }}</span>
          </div>
        </div>
      </el-form-item>

      <div>
        <el-button type="primary" size="default" @click="saveSettings">保存设置</el-button>
      </div>
    </el-form>
  </ContentWrap>

  <el-drawer v-model="drawer" :title="titleText" :direction="direction" :before-close="handleClose">
    <el-form ref="formRef" label-width="80px" label-position="top">
      <div v-show="isShow == 1">
        <el-form-item label="参与考勤人员">
          <div style="max-width: 350px">
            <el-button type="primary" size="default" @click="$refs.orgPicker.show()"
              >请选择考勤人员</el-button
            >
            <div style="margin-top: 5px">
              <el-tag
                size="small"
                style="margin: 5px"
                closable
                v-for="(dept, i) in Uservalue"
                @close="delDept(i, 1)"
                >{{ dept.name }}</el-tag
              >
            </div>
          </div>
        </el-form-item>
        <el-form-item label="无需考勤人员">
          <div style="max-width: 350px">
            <el-button type="primary" size="default" @click="$refs.orgPicker1.show()"
              >请选择无需考勤人员</el-button
            >
            <div style="margin-top: 5px">
              <el-tag
                size="small"
                style="margin: 5px"
                closable
                v-for="(dept, i) in Uservalue1"
                @close="delDept(i, 2)"
                >{{ dept.name }}</el-tag
              >
            </div>
          </div>
        </el-form-item>
      </div>
      <!--  -->
      <div v-show="isShow == 2">
        <el-form-item label="考勤类型">
          <el-radio-group v-model="radio1" class="ml-4">
            <el-radio value="1" size="large">固定班制(固定时间上下班)</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="工作日设置">
          <div style="flex: 1"
            >快捷设置班次： {{ `班次 ${zhanshiPersons.name} ${zhanshiPersons.timeStr}` }}</div
          >
          <el-button type="primary" link size="default" @click="changeShift">更改班次</el-button>
        </el-form-item>
        <el-table :data="timeList" @selection-change="handleSelectionChange" ref="multipleTableRef">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column align="center" label="工作日" prop="week" />
          <el-table-column align="center" label="班次时间段" prop="timeperiod"> </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button link type="primary" @click="dandubanci(scope.row.id)">
                更改班次
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!--  -->
      <div v-show="isShow == 3">
        <el-form-item label="考勤方式">
          <div>地点</div>
        </el-form-item>
        <el-button size="default" @click="addLocation">添加</el-button>
      </div>
      <div v-show="isShow == 3" style="margin-top: 20px">
        <!-- {{
        SubData && SubData.newAddress
          ? SubData.cityValues ||
            SubData.newAddress.district + SubData.newAddress.name + SubData.newAddress.address
          : ''
      }} -->
        <el-table :data="addressList" v-show="addressList.length > 0">
          <el-table-column align="center" label="考勤地址" prop="location" />
          <el-table-column align="center" label="有效范围" prop="radius">
            <template #default="scope">
              <el-select v-model="scope.row.radius">
                <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.Meters"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button link type="danger" @click="deleteDitu(scope.$index)"> 删除 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-show="isShow == 4">
        <el-form-item>
          <template #label>
            <span class="mRt">拍照打卡</span>
            <el-switch v-model="photographFlag" active-value="0" inactive-value="1" />
          </template>
          <div class="tishi"> 员工打卡时需拍照，照片会存档 </div>
        </el-form-item>
        <!--  -->
        <el-form-item>
          <template #label>
            <span class="mRt">人脸识别</span>
            <el-tooltip class="box-item" effect="dark" content="该功能暂未开放" placement="right">
              <el-switch
                v-model="facialRecognitionFlag"
                disabled
                active-value="0"
                inactive-value="1"
              />
            </el-tooltip>
            <div class="tishi"> 员工打卡时自动验证人脸，照片不存档 </div>
          </template>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="drawer = false">取消</el-button>
        <el-button type="primary" @click="allConfirmed"> 确定 </el-button>
      </div>
    </template>
  </el-drawer>

  <!--选择班次  -->
  <el-dialog v-model="shiftDialog" title="选择班次" width="600" class="tach">
    <el-table v-loading="loading" :data="bcList" highlight-current-row>
      <el-table-column width="26" align="center">
        <template #default="scope">
          <!-- <el-radio-group v-model="radio" class="ml-4"> -->
          <el-radio
            v-model="radio"
            :label="scope.$index"
            @change="handleRadioChange(scope.$index, scope.row)"
          ></el-radio>
          <!-- </el-radio-group> -->
        </template>
      </el-table-column>
      <el-table-column align="center" label="班次名称" prop="name" />
      <el-table-column align="center" label="考勤时间" prop="timeStr" />
    </el-table>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="shiftDialog = false">取消</el-button>
        <el-button type="primary" @click="quedBC"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
  <org-picker multiple ref="orgPicker" :selected="Uservalue" @ok="selected" />
  <org-picker multiple ref="orgPicker1" :selected="Uservalue1" @ok="selected1" />
  <mapes ref="map" @success="getSubData" />
</template>

<script lang="ts" setup>
import { useWFlowStore } from '@/store/modules/wflow'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import * as bcApi from '@/api/system/attendance_bc'

import { defaultProps, handleTree } from '@/utils/tree'
import { Icon } from '@/components/Icon'
import { Back } from '@element-plus/icons-vue'
import OrgPicker from '@/components/common/OrgPicker.vue'
import mapes from './map.vue'

defineOptions({ name: 'SystemRoleAssignMenuForm' })
// 新的
import type { UploadInstance, UploadProps, UploadRawFile, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

import { el, fa, tr } from 'element-plus/es/locale'
import { log } from 'console'
import { iteratee, kebabCase } from 'lodash-es'
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const drawer = ref(false)
const flag = ref(true)
const shiftDialog = ref(false)
const checked1 = ref(true)
const loading = ref(false) // 列表的加载中
const map = ref()

const total = ref(0) // 列表的总页数
const isShow = ref(1)
const photographFlag = ref('1')
const facialRecognitionFlag = ref('1')
const isDeleteDitu = ref(1)
const titleText = ref('')
const radio1 = ref('')
const radio = ref('')
const selectedPersons = ref('')
const zhanshiPersons = ref('')
const direction = ref('rtl')
const kaoQinName = ref('')
const modelMeters = ref('')
const editId = ref('')
const editRulesId = ref('')
const isId = ref('')
const backTitle = ref('')
const orgPicker = ref(null)
const Uservalue = ref([])
const Uservalue1 = ref([])
const Bglist = ref([])
const bcList = ref([])
const newUser = ref([])
const newUser1 = ref([])
const addressList = ref<any>([])
const SubData = ref({})
const workDayList = ref([0, 0, 0, 0, 0, 0, 0])
const selectionData = ref([])
const isIdArry = ref([])
const newArry = ref([])
const isSingle = ref(true)
const isQueding = ref(true)
const editKqinList = ref([])
const multipleTableRef = ref()
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})

const timeList = ref([
  {
    week: '周一',
    id: 0
  },
  {
    week: '周二',
    id: 1
  },
  {
    week: '周三',
    id: 2
  },
  {
    week: '周四',
    id: 3
  },
  {
    week: '周五',
    id: 4
  },
  {
    week: '周六',
    id: 5
  },
  {
    week: '周日',
    id: 6
  }
])

const options = ref([
  {
    id: 0,
    Meters: '0米'
  },
   {
    id: 20,
    Meters: '20米'
  },
   {
    id: 100,
    Meters: '100米'
  },
  {
    id: 200,
    Meters: '200米'
  },
  {
    id: 300,
    Meters: '300米'
  },

  {
    id: 400,
    Meters: '400米'
  },
  {
    id: 500,
    Meters: '500米'
  },
  {
    id: 600,
    Meters: '600米'
  },
  {
    id: 700,
    Meters: '700米'
  },
  {
    id: 800,
    Meters: '800米'
  },
  {
    id: 900,
    Meters: '900米'
  },
  {
    id: 1000,
    Meters: '1000米'
  },
  {
    id: 2000,
    Meters: '2000米'
  },
  {
    id: 3000,
    Meters: '3000米'
  }
])
const getSubData = (val) => {
  console.log(val, '子组件')
  modelMeters.value = val.radius
  isDeleteDitu.value = 1
  if(!addressList || addressList.value.length == 0) {
    addressList.value = [
      {
        location: val.cityValues || val.newAddress.district + val.newAddress.name + val.newAddress.address,
        radius: val.radius,
        longitude:val.newAddress.location.lng ? val.newAddress.location.lng : val.newAddress.lng,
        latitude:val.newAddress.location.lat ? val.newAddress.location.lat : val.newAddress.lat
      }
    ]
  }else{
    addressList.value.push({
      location: val.cityValues || val.newAddress.district + val.newAddress.name + val.newAddress.address,
      radius: val.radius,
      longitude:val.newAddress.location.lng ? val.newAddress.location.lng : val.newAddress.lng,
      latitude:val.newAddress.location.lat ? val.newAddress.location.lat : val.newAddress.lat
    })
  }
  SubData.value = val
}
// 新增考勤组
const addAttendanceGroup = () => {
  flag.value = false
  workDayList.value = [0, 0, 0, 0, 0, 0, 0]
  backTitle.value = '新增考勤组'
  getbcList()
  isQueding.value = true
  kaoQinName.value = ''
  radio1.value = ''
  selectedPersons.value = ''
  newUser.value = []
  newUser1.value = []
  Uservalue.value = []
  Uservalue1.value = []
  addressList.value = []
  SubData.value = {}
  isDeleteDitu.value = 1
  editId.value = ''
  selectionData.value = []
  photographFlag.value = '1'
  facialRecognitionFlag.value = '1'
}

const goBack = () => {
  flag.value = true
}
// 右侧弹窗确定
const allConfirmed = () => {
  // console.log(selectionData.value)
  if (isShow.value == 2) {
    //为了判断新增和编辑时workDayList的值,isShow不等于2的话workDayList的值一直是0或者为null
    workDayList.value = [0, 0, 0, 0, 0, 0, 0]
    selectionData.value.map((item) => {
      workDayList.value.forEach((it, i) => {
        if (item.id == i) {
          workDayList.value[item.id] = item.value
        }
      })
    })
  }
  // console.log(workDayList.value, 'workDayList')
  // console.log(selectionData.value, 'selectionData')

  // if (!isSingle.value) {
  //   console.log(isIdArry.value)
  //   console.log(selectionData.value)
  //   // selectionData.value.map(k=>{
  //   //   if(k.week){
  //   //     console.log('hahah');
  //   //   }
  //   // })
  //   isIdArry.value.map((n) => {
  //     workDayList.value.forEach((it, i) => {
  //       if (n.id == i) {
  //         workDayList.value[i] = n.value
  //       }
  //     })
  //   })
  // } else {
  //   workDayList.value = [0, 0, 0, 0, 0, 0, 0]
  //   selectionData.value.map((item) => {
  //     workDayList.value.forEach((it, i) => {
  //       if (item.id == i) {
  //         workDayList.value[item.id] = selectedPersons.value.id
  //       }
  //     })
  //   })
  // }
  if (isShow.value == 2) {
    isQueding.value = false
  }
  // console.log(workDayList.value, 'workDayList')
  // console.log(selectedPersons.value, 'selectedPersons')
  // console.log(selectionData.value, 'selectionData')
  // return
  // console.log(...Uservalue.value)
  newUser.value = []
  newUser1.value = []
  newUser.value = Uservalue.value
  newUser1.value = Uservalue1.value
  drawer.value = false
}
const toggleSelection = (rows, id) => {
  if (rows) {
    rows.forEach((row) => {
      // console.log(id)
      row.value = id
      multipleTableRef.value!.toggleRowSelection(row, true)
    })
  }
  // else {
  //   multipleTableRef.value!.clearSelection()
  // }
  // console.log(id, 'idiid')
  // if (backTitle.value == '新增考勤组') {
  //   if (id) {
  //     selectionData.value.map((item) => {
  //       console.log(item,'sdsdsdsdsdsdsdsdsdsd');

  //       item.value = id
  //     })
  //   }
  // }else{
  //   // console.log(id);

  // }

  // console.log(selectionData.value, 'selectionDataselectionData')
}
const dandubanci = (id) => {
  // console.log(id)
  isSingle.value = false
  isId.value = id
  shiftDialog.value = true
  // getbcList()
}

const quedBC = () => {
  // console.log(timeList.value)
  // console.log(isIdArry.value)
  if (!isSingle.value) {
    // 单个修改
    toggleSelection([timeList.value[isId.value]]) //必须放前面
    timeList.value.map((item, i) => {
      if (item.id == isId.value) {
        timeList.value[i].timeperiod =
          '班次' + selectedPersons.value.name + selectedPersons.value.timeStr
      }
    })
    selectionData.value.map((ks) => {
      if (ks.id == isId.value) {
        ks.value = selectedPersons.value.id
      }
    })
  } else {
    toggleSelection(timeList.value) //必须放前面
    timeList.value.forEach((it) => {
      selectionData.value.map((item) => {
        if (it.id == item.id) {
          timeList.value[it.id].timeperiod =
            '班次' + selectedPersons.value.name + selectedPersons.value.timeStr
          item.value = selectedPersons.value.id
          zhanshiPersons.value = selectedPersons.value
        }
      })
    })
  }

  shiftDialog.value = false
  // console.log(selectedPersons.value)
}
// 更改班次
const changeShift = () => {
  isSingle.value = true
  shiftDialog.value = true
  // getbcList()
}
// 复选框
const handleSelectionChange = (val) => {
  // console.log(val)
  // console.log(selectionData.value, '找打')

  selectionData.value = val
  // console.log(selectionData.value)

  // 遍历timeList，查找selectionData中是否存在对应的id
  timeList.value.forEach((item) => {
    const found = selectionData.value.find((item2) => item2.id === item.id)
    if (!found) {
      // 如果找不到，修改timeList中的值
      item.timeperiod = '休息'
      // item2.value = 0
    } else {
      selectionData.value.map((k) => {
        // k.value = selectedPersons.value.id
        if (k.timeperiod == '休息') {
          k.value = zhanshiPersons.value.id
          k.timeperiod = '班次' + zhanshiPersons.value.name + zhanshiPersons.value.timeStr
        }
      })
    }
  })
}

// 添加地点
const addLocation = () => {
  map.value.open()
}

const setUpDra = (v) => {
  isShow.value = v
  switch (v) {
    case 1:
      titleText.value = '考勤人员'
      if (newUser.value.length <= 0) {
        Uservalue.value = []
      }
      if (newUser1.value.length <= 0) {
        Uservalue1.value = []
      }
      break
    case 2:
      titleText.value = '考勤时间'
      getbcList()
      break
    case 3:
      titleText.value = '打卡方式'
      break
    case 4:
      titleText.value = '拍照与人脸识别'
      break
  }
  drawer.value = true
}
// 保存设置
const saveSettings = async () => {
  // console.log(workDayList.value, 'workDayListssssss')
  // return
  // console.log(SubData.value, 'ssssss')
  if (!kaoQinName.value) return message.error('考勤组名称不能为空')
  if (newUser.value.length <= 0) return message.error('请选择参与部门人员')
  if (Object.keys(addressList.value).length === 0) return message.error('请选择地点打卡')
  let newArry = []
  newArry = newUser.value.map((item) => {
    return {
      objectType: item.type == 'dept' ? 'dept' : 'user',
      groupObjectId: item.id
    }
  })

  let newArry1 = []
  newArry1 = newUser1.value.map((item) => {
    return {
      userId: item.id,
      name: item.name
    }
  })

  // console.log(...newArry, 'czxc')
  // console.log(selectedPersons.value, 'radioradioradio')
  const rules = []
  addressList.value.forEach((item) => {
    rules.push({
      id: item.id ? item.id : '',
      attendanceType: item.attendanceType ? item.attendanceType : 0,
      longitude: item.longitude,
      latitude: item.latitude,
      radius: item.radius,
      location: item.location
    })
  })
  let obj
  if (isDeleteDitu.value == 1) {
    obj = {
      name: kaoQinName.value,
      type: 0,
      shiftId: selectedPersons.value.id || 1,
      id: editId.value || '',
      rules: rules,
      attendanceObjects: [
        // {
        //   objectType: 'user、dept',
        //   groupObjectId: 1
        // }
        ...newArry
      ],
      workDayList: workDayList.value,
      needPhoto: photographFlag.value == '1' ? '0' : '1',
      needFaceVerify: facialRecognitionFlag.value == '1' ? '0' : '1',
      userNotAttendList: [...newArry1]
    }
  } else {
    obj = {
      id: editId.value,
      name: kaoQinName.value,
      type: 0,
      shiftId: selectedPersons.value.id || 1,
      rules: rules,
      attendanceObjects: [
        // {
        //   objectType: 'user、dept',
        //   groupObjectId: 1
        // }
        ...newArry
      ],
      workDayList: workDayList.value,
      needPhoto: photographFlag.value == '1' ? '0' : '1',
      needFaceVerify: facialRecognitionFlag.value == '1' ? '0' : '1',
      userNotAttendList: [...newArry1]
    }
  }
  // console.log(obj, 'objooooooo')
  // return
  try {
    await bcApi.addOrUpdate(obj)
    groupList()
    flag.value = true

    message.success(t('common.createSuccess'))
  } finally {
  }
}

const selected = (va) => {
  // console.log(va, 'vvvvvvvvvvvvvv')
  let fa = true
  if (Uservalue1.value.length > 0) {
    va.map((item, i) => {
      Uservalue1.value.forEach((it, i1) => {
        if (item.id == it.id) {
          // console.log('进来fff')
          fa = false
        }
      })
    })
  }
  // console.log('继续往下走')
  if (fa) {
    Uservalue.value = va.map((item, i) => {
      return {
        name: item.name,
        id: item.id,
        type: item.type
      }
    })
  }
}
const selected1 = (va) => {
  // console.log(va, '11111111111')
  if (Uservalue.value.length > 0) {
    va.map((item, i) => {
      Uservalue.value.forEach((it, i1) => {
        if (item.id == it.id) {
          // console.log('进来')
          Uservalue.value.splice(i1, 1)
        }
      })
    })
  }

  Uservalue1.value = va.map((item, i) => {
    return {
      name: item.name,
      id: item.id,
      type: item.type
    }
  })
}
const delDept = (i, n) => {
  // console.log(Uservalue.value, 'Uservalue.valueUservalue.value')
  if (n == 1) {
    Uservalue.value.splice(i, 1)
  } else {
    Uservalue1.value.splice(i, 1)
  }
}

const getbcList = async () => {
  loading.value = true
  try {
    const data = await bcApi.getListData({ pageNo: 1, pageSize: 10000 })
    // console.log(data, '哈啊啊哈')
    // list.value = data
    bcList.value = data.list
    if (backTitle.value == '新增考勤组') {
      selectedPersons.value = data.list[0]
      zhanshiPersons.value = data.list[0]
      workDayList.value.forEach((it, i) => {
        if (i != 5 && i != 6) {
          workDayList.value[i] = data.list[0].id
        }
      })
      if (isQueding.value) {
        selectionData.value = []
        multipleTableRef.value!.clearSelection()
        timeList.value.map((item, i) => {
          if (i != 5 && i != 6) {
            item.timeperiod = '班次' + data.list[0].name + data.list[0].timeStr
            toggleSelection([timeList.value[i]], data.list[0].id)
          } else {
            item.timeperiod = '休息'
          }
        })
        // console.log(selectionData.value)
      }
      radio.value = 0
    } else if (backTitle.value == '编辑考勤组') {
      multipleTableRef.value!.clearSelection()

      // console.log('编辑')
      // console.log(editKqinList.value.selectedClass['2'])
      // zhanshiPersons.value = editKqinList.value.selectedClass['2']
      selectionData.value = []
      console.log(selectionData.value, 'dasda')

      editKqinList.value.workDayList.map((item, i) => {
        // console.log(i)
        // console.log(item);
        if (item != 0) {
          zhanshiPersons.value = editKqinList.value.selectedClass[item]
        }

        timeList.value.map((it) => {
          // console.log(it.id);
          if (i == it.id) {
            for (let k in editKqinList.value.selectedClass) {
              if (item == k) {
                // workDayList.value[i] = editKqinList.value.selectedClass[k].id
                console.log(editKqinList.value.selectedClass[k])
                it.timeperiod =
                  '班次' +
                  editKqinList.value.selectedClass[k].name +
                  editKqinList.value.selectedClass[k].timeStr
                toggleSelection([timeList.value[i]], editKqinList.value.selectedClass[k].id)
                // console.log(editKqinList.value.selectedClass[k].id);
              }
            }
          }
        })
        // console.log(item);
      })
    }

    // console.log(radio.value)

    // toggleSelection(timeList.value)

    // total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  // console.log(555)
  // console.log(selectionData.value)
  if (isShow.value == 2) {
    ElMessageBox.confirm('确定保存当前设置?')
      .then(() => {
        allConfirmed()
        // drawer.value = false
      })
      .catch(() => {
        // catch error
      })
  } else {
    drawer.value = false
  }
}
// 表个更改班次单选按钮
const handleRadioChange = (index, row) => {
  selectedPersons.value = row
}
// 获取考勤组表格数据
const groupList = async () => {
  loading.value = true
  try {
    const data = await bcApi.groupList(queryParams)
    Bglist.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}
// 编辑考勤组
const openForm = async (id) => {
  flag.value = false
  isDeleteDitu.value = 2
  backTitle.value = '编辑考勤组'
  const data = await bcApi.groupKqList(id)
  editKqinList.value = data
  kaoQinName.value = data.name
  photographFlag.value = data.needPhoto == '1' ? '0' : '1'
  facialRecognitionFlag.value = data.needFaceVerify == '1' ? '0' : '1'
  Uservalue.value = data.attendanceObjects.map((item, i) => {
    return {
      name: item.objectName,
      id: item.groupObjectId + '',
      type: item.objectType
    }
  })

  Uservalue1.value = data.userNotAttendList.map((item, i) => {
    return {
      name: item.name,
      id: item.userId + ''
    }
  })

  newUser.value = data.attendanceObjects.map((item, i) => {
    return {
      name: item.objectName,
      id: item.groupObjectId,
      type: item.objectType
    }
  })
  newUser1.value = data.userNotAttendList.map((item, i) => {
    return {
      name: item.name,
      id: item.userId
    }
  })
  addressList.value = data.rules
  SubData.value = data.rules[0]
  editId.value = id
  editRulesId.value = data.rules[0].id
  modelMeters.value = data.rules[0].radius
  workDayList.value = [0, 0, 0, 0, 0, 0, 0]
  selectionData.value = []

  editKqinList.value.workDayList.map((item, i) => {
    if (item != 0) {
      zhanshiPersons.value = editKqinList.value.selectedClass[item]
    }
    // console.log(i)
    console.log(editKqinList.value.selectedClass, 'editKqinList.value.selectedClass')

    timeList.value.map((it) => {
      // console.log(it.id);
      if (i == it.id) {
        for (let k in editKqinList.value.selectedClass) {
          if (item == k) {
            workDayList.value[i] = editKqinList.value.selectedClass[k].id
            selectionData.value.push(timeList.value[i])
            // toggleSelection([timeList.value[i]], editKqinList.value.selectedClass[k].id)
          }
        }
      }
    })
    // console.log(item);
  })

  // getbcList()

  // let newarr = []
}
// 删除考勤组
const handleDelete = async (id) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await bcApi.deleteKq(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await groupList()
  } catch {}
}
// 删除选择后的打卡地点信息
const deleteDitu = (index) => {
  addressList.value.splice(index, 1)
  isDeleteDitu.value = 1
  SubData.value = []
}
/** 初始化 **/
onMounted(() => {
  if (useWFlowStore().isJump) {
    useWFlowStore().setIsJump(false)
    addAttendanceGroup()
  }
  groupList()
  // getbcList()
})
</script>
<style lang="scss" scoped>
.top {
  font-weight: bold;
  border-bottom: 1px solid #f0f0f0;
  padding: 10px;
  margin-bottom: 10px;
}
.mRt {
  margin-right: 15px;
}
.tishi {
  color: #808080;
}
</style>
