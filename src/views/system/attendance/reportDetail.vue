<template>
  <el-card class="oaCard">
    <el-page-header :icon="ArrowLeft" @back="closeChild" class="custom-page-header">
      <template #content>
        <span class="text-large font-600 mr-3">{{ rowInfo?.entity?.name }}</span>
      </template>
    </el-page-header>
    <el-form :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item>
        <el-date-picker v-model="timeValue" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" @change="timeChange" :clearable="false" class="!w-220px" value-format="YYYY-MM-DD">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-select v-model="selectType" class="!w-140px" @change="selectChange">
          <el-option label="全公司" value="0" />
          <el-option label="考勤组" value="1" />
          <el-option label="部门/人员" value="2" />
          <!-- <el-option label="已离职人员" value="3" /> -->
        </el-select>
      </el-form-item>
      <el-form-item v-if="selectType == 1">
        <el-select v-model="groupIds" class="!w-280px" filterable multiple collapse-tags collapse-tags-tooltip
          @visible-change="groupVisible" @change="groupChange">
          <el-option :label="item.name" :value="item.id" v-for="(item, index) in groupList" :key="index" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="selectType == 2">
        <div @click="inputFocus" class="deptClass">
          <el-input readonly :placeholder="targetUser.length > 0 ? '' : '请选择部门/人员'"
            :style="{ width: dynamicWidth + 'px' }" />
          <org-picker title="请选择" ref="orgPicker" multiple :selected="targetUser" @ok="selected" />
          <div v-if="targetUser.length > 0" class="tagItem" ref="tagItemRef">
            <el-tag type="info">{{ targetUser[0].name }}</el-tag>
            <el-tooltip effect="light">
              <template #content>
                <el-tag type="info" v-for="(item, index) in targetUser.slice(1)" :key="index" class="custom-tagHover">
                  {{ item.name }}
                </el-tag>
              </template>
              <el-tag type="info" class="isAddBox" v-if="targetUser.length > 1">+1</el-tag>
            </el-tooltip>
          </div>
        </div>
      </el-form-item>
      <el-form-item>
        <!-- <el-checkbox v-model="checkBoxValue">包含90天内离职人员</el-checkbox> -->
      </el-form-item>
    </el-form>
    <div class="mb-15px">
      <el-button type="primary" icon="el-icon-download" @click="handelExport">导出报表</el-button>
      <el-button @click="handelRecord">导出记录</el-button>
    </div>
    <el-table v-loading="loading" :data="tableData" :show-overflow-tooltip="true">
      <el-table-column v-for="(item, index) in headerList" :key="index" :prop="item.dataIndex" :label="item.title"
        min-width="170" :fixed="item.dataIndex == 'userName'" />
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getBody" />
  </el-card>
  <record ref="recordRef"></record>
</template>
<script setup type="ts">
import { dateFormatter } from '@/utils/formatTime'
import { attendanceApi } from '@/api/system/attendance/index'
import * as bcApi from '@/api/system/attendance_bc'
import OrgPicker from '@/components/common/OrgPicker.vue'
import record from './reportRecord.vue'
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10
})
const searchParams = reactive({
  fromDate: '',
  toDate: '',
  groups: [],
  departments: [],
  users: []
})
const timeValue = ref([]) //时间范围
const selectType = ref('0') //类型
const groupIds = ref([]) //考勤组所选
const groupList = ref([]) //考勤组下拉
const targetUser = ref([]) //部门人员下拉
const checkBoxValue = ref(false) //复选
// 列表
const loading = ref(false)
const headerList = ref([])
const tableData = ref([])
const total = ref(0)
const rowInfo = ref({})
const open = (row) => {
  rowInfo.value = row
  console.log(rowInfo.value)
  loading.value = true
  const dates = getRecent15Days()
  timeValue.value = [dates[0], dates[dates.length - 1]]
  searchParams.fromDate = dates[0]
  searchParams.toDate = dates[dates.length - 1]
  groupIds.value = []
  targetUser.value = []
  getHeader()
}
// 表头数据
const getHeader = async () => {
  try {
    const res = await attendanceApi.getRecordHeader()
    headerList.value = res.headerColumns
    getBody()
  } finally {
    // loading.value = false
  }
}
// body数据
const getBody = async () => {
  try {
    const res = await attendanceApi.getRecordBody({ ...queryParams, ...searchParams })
    total.value = res.totalRows
    tableData.value = res.rows.map((row) => {
      const formattedRow = {}
      headerList.value.forEach((column) => {
        const value = row[column.dataIndex].text
        formattedRow[column.dataIndex] = value === 'null' ? null : value
      })
      return formattedRow
    })
  } finally {
    loading.value = false
  }
}
// 日期范围选择
const timeChange = (e) => {
  searchParams.fromDate = e[0]
  searchParams.toDate = e[1]
  handleSearch()
}
// 类型change
const isFirst = ref(true)
const selectChange = (e) => {
  groupIds.value = []
  targetUser.value = []
  searchParams.groups = []
  searchParams.departments = []
  searchParams.users = []
  if (e == 0) {
    handleSearch()
  }
  if (e == 1 && isFirst) {
    isFirst.value = false
    getGroupList()
  }
}
// 考勤组change事件
const isChange = ref(false)
const groupChange = (e) => {
  if (e) {
    isChange.value = true
  } else {
    isChange.value = false
  }
}
// 考勤组下拉框隐藏时
const groupVisible = (e) => {
  if (isChange && !e) {
    console.log(groupIds.value)
    searchParams.groups = groupIds.value
      .filter((item1) => groupList.value.some((item2) => item1 == item2.id))
      .map((item1) => {
        const item2 = groupList.value.find((item2) => item1 == item2.id)
        return {
          groupId: item2.id,
          name: item2.name
        }
      })
    handleSearch()
  }
}
// 搜索
const handleSearch = () => {
  queryParams.pageNo = 1
  queryParams.pageSize = 10
  loading.value = true
  // getHeader()
  getBody()
}
// 考勤组下拉
const getGroupList = async () => {
  try {
    const res = await bcApi.groupList({
      pageNo: 1,
      pageSize: 100
    })
    groupList.value = res.list
  } finally {
    loading.value = false
  }
}
// 点击选择部门
const orgPicker = ref()
const inputFocus = (e) => {
  if (e.target.tagName.toLowerCase() === 'input') {
    return
  }
  orgPicker.value.show()
}
// 接收部门信息
const dynamicWidth = ref(240)
const tagItemRef = ref()
const selected = (users) => {
  targetUser.value = []
  searchParams.departments = []
  searchParams.users = []
  users.forEach((item) => {
    targetUser.value.push({
      id: item.id,
      name: item.name,
      type: item.type
    })
    if (item.type == 'dept') {
      searchParams.departments.push({
        deptId: item.id,
        name: item.id
      })
    }
    if (item.type == 'user') {
      searchParams.users.push({
        userId: item.id,
        name: item.id
      })
    }
  })
  nextTick(() => {
    console.log(tagItemRef.value.getBoundingClientRect().width)
    if (tagItemRef.value.getBoundingClientRect().width > 230) {
      dynamicWidth.value = tagItemRef.value.getBoundingClientRect().width + 20
    } else {
      dynamicWidth.value = 240
    }
  })
  handleSearch()
}
// 获取近十五天
function getRecent15Days() {
  const today = new Date()
  const recent15Days = []
  for (let i = 0; i < 15; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)
    const formattedDate = date.toISOString().split('T')[0]
    recent15Days.unshift(formattedDate)
  }
  return recent15Days
}
// 创建导出
const handelExport = async () => {
  try {
    const res = await attendanceApi.createReportExportTask({
      // ...queryParams,
      reportType: rowInfo.value.entity.reportType,
      tableId: rowInfo.value.entity.id,
      fromDate: searchParams.fromDate,
      toDate: searchParams.toDate,
      departmentList: searchParams.departments,
      userList: searchParams.users,
      groupList: searchParams.groups,
    })
    handelRecord()
  } finally {
  }
}
// 导出记录
const recordRef = ref()
const handelRecord = () => {
  recordRef.value.open(rowInfo.value.entity.reportType)
}
// 关闭
const closeChild = () => {
  emit('close')
}
const emit = defineEmits(['close'])
defineExpose({
  open
})
</script>
<style lang="less" scoped>
:deep(.el-form--inline .el-form-item) {
  margin-right: 15px;
}

.deptClass {
  position: relative;

  .el-input {
    pointer-events: none;
  }

  .tagItem {
    position: absolute;
    left: 10px;
    top: 0;
    white-space: nowrap;

    .el-tag {
      margin-right: 5px;
    }
  }

  .isAddBox {
    position: relative;
    cursor: pointer;
  }
}
</style>
<style>
.custom-tagHover {
  margin-right: 5px !important;
}
</style>
