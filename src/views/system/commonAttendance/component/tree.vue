<template>
  <div>
    <el-dialog title="配置" v-model="configVisible" width="600" top="10vh">
      <el-input v-model="filterText" placeholder="输入名称过滤" clearable />
      <el-scrollbar style="height: 500px;">
        <div class="dept-tree-container">
          <el-tree ref="treeRef" :data="processedDeptData" :props="treeProps" node-key="userId" default-expand-all
            :expand-on-click-node="false" show-checkbox :filter-node-method="filterMethod" >
            <template #default="{ node, data }">
              <div class="custom-tree-node">
                <div class="dept-name">{{ node.label }}</div>
                <div class="dept-info">
                  <span class="user-count" v-if="data.userCount">({{ data.userCount }}人)</span>
                  <!-- <span v-if="data.leaders?.length" class="leaders">
              负责人：{{ data.leaders.map(l => l.name).join('，') }}
            </span> -->
                </div>
              </div>
            </template>
          </el-tree>

        </div>
      </el-scrollbar>


      <template #footer>
        <div class="dialog-footer">
          <el-button @click="configVisible = false">取消</el-button>
          <el-button type="primary" @click="addSubmit()">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>
<script lang='ts' setup>
// import groupPickerView from './component/picker.vue'
import { getShiftList, attendanceShiftAdd, getDeptListNew, salaryRuleUserBind, getSalaryRuleUserBindList } from '@/api/system/commonAttendance';
import { ref, reactive, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import {
  Plus,
  Minus
} from '@element-plus/icons-vue'
const message = useMessage()



const dataType = ref(1)
const ruleId = ref(1)

const configVisible = ref(false)

const show = (dataTypeVal, ruleIdVal) => {

  console.log("show", dataType, ruleId)

  dataType.value = dataTypeVal
  ruleId.value = ruleIdVal

  handleConfig()
  configVisible.value = true





}

defineExpose({
  show
});

// getDeptListNew,salaryRuleUserBind,getSalaryRuleUserBindList


// const groupPicker = ref()


const configTreeData = ref([])

// 处理后的树形数据
const processedDeptData = ref([]);

// 树形组件配置
const treeProps = ref({
  label: 'name',
  children: 'children'
});

// 初始化处理
const processData = (data) => {
  return data.map(item => {
    // 部门节点
    const deptNode = {
      id: item.id,
      userId: `dept_${item.id}`, // 部门节点使用特殊ID
      name: item.name,
      type: 'dept',
      userCount: item.userCount,
      children: []
    };

    // 添加领导节点
    if (item.leaders?.length) {
      deptNode.children.push(
        ...item.leaders.map(leader => ({
          ...leader,
          type: 'leader',
          userId: leader.userId.toString() // 确保userId是字符串
        }))
      );
    }

    // 递归处理子部门
    if (item.children?.length) {
      deptNode.children.push(...processData(item.children));
    }

    return deptNode;
  });
};


// 搜索功能
const filterText = ref('');
const treeRef = ref();

watch(filterText, (val) => {
  treeRef.value?.filter(val);
});


const filterMethod = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

const selectedUsers = ref([]);

const handleConfig = async (row) => {
  console.log("handleConfig", row)

  let getDeptListNewRes = await getDeptListNew({
    dataType: dataType.value,
    ruleId: ruleId.value
  })

  configTreeData.value = getDeptListNewRes.data
  processedDeptData.value = processData(configTreeData.value);

  let getSalaryRuleUserBindListRes = await getSalaryRuleUserBindList({
    dataType: dataType.value,
    ruleId: ruleId.value
  })

  selectedUsers.value =getSalaryRuleUserBindListRes.data

  autoCheckUsers()

  console.log("getSalaryRuleUserBindListRes", getSalaryRuleUserBindListRes)

  configVisible.value = true

}



const autoCheckUsers = async () => {
  console.log("autoCheckUsers")
  await nextTick(); // 等待DOM更新
  if (!treeRef.value) return;

  // 提取需要选中的userId数组
  const userIdsToCheck = selectedUsers.value.map(u => u.userId);

  console.log("userIdsToCheck", userIdsToCheck)
  
  // 设置选中状态
  treeRef.value.setCheckedKeys(userIdsToCheck);
};



const addSubmit = async () => {
  console.log("addSubmit")
  const selectedNodes = treeRef.value.getCheckedNodes(true)
  // console.log("selectedNodes", selectedNodes)
  // const selectedDeptIds = selectedNodes.map(node => node.id)
  // console.log("selectedDeptIds", selectedDeptIds)
  // const selectedDeptNames = selectedNodes.map(node => node.name)
  // console.log("selectedDeptNames", selectedDeptNames)
  const selectedLeaders = selectedNodes.filter(node => node.type === 'leader')
  console.log("selectedLeaders", selectedLeaders)
  const selectedLeadersIds = selectedLeaders.map(node => node.userId)
  console.log("selectedLeadersIds", selectedLeadersIds)
  // const selectedLeadersNames = selectedLeaders.map(node => node.name)
  // console.log("selectedLeadersNames", selectedLeadersNames)

  const params = {
    ruleId: ruleId.value,
    dataType: dataType.value,
    userIds: selectedLeadersIds,
  }
  // console.log("params", params)
  const salaryRuleUserBindRes = await salaryRuleUserBind(params)
  // console.log("attendanceShiftAddRes", salaryRuleUserBindRes)
  if (salaryRuleUserBindRes.code === 0) {
    message.success('配置成功')
    configVisible.value = false
  } else {
    message.error(salaryRuleUserBindRes.msg)
  }
}



</script>

<style scoped lang='less'>
.dept-tree-container {
  padding: 20px;
  max-width: 800px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.dept-name {
  font-weight: 600;
  color: #333;
}

.dept-info {
  margin-left: 20px;
  color: #666;
}

.user-count {
  margin-right: 15px;
}

.leaders {
  font-size: 12px;
  color: #999;
}

:deep(.el-tree-node__content) {
  height: 40px;
}
</style>