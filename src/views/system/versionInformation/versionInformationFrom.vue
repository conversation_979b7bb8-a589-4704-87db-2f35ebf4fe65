<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="900">
    <el-form ref="formRef" v-loading="formLoading" :model="formData" :rules="formRules" label-width="100px" :element-loading-text="formLoadingText">
      <el-form-item label="封面" prop="coverPic" >
        <UploadImg v-model="formData.coverPic" :limit="1">
          <template #tip>建议尺寸 654*280</template>
        </UploadImg>
      </el-form-item>
      <el-form-item label="更新图片" prop="updatePic" >
        <UploadImg v-model="formData.updatePic" :limit="1">
          <!-- <template #tip>建议宽度 750px</template> -->
        </UploadImg>
      </el-form-item>

      <el-form-item label="版本号" prop="versionNo">
        <el-input v-model="formData.versionNo" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="版本标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入版本标题" />
      </el-form-item>
      <el-form-item label="版本内容" prop="content" class="editor-div">
        <!-- <Editor v-model="formData.content" height="300px" class="isEditor" /> -->
        <el-input type="textarea" v-model="formData.content" placeholder="请输入版本内容" />
      </el-form-item>
      <el-form-item label="安卓安装包" prop="androidUrl">
        <el-upload action="" :auto-upload="false" :multiple="false" :file-list="androidFileList" :on-change="onAndroidUploadChange"
          :on-remove="onAndroidUploadRemove" :on-exceed="onAndroidUploadExceed" :limit="1" accept=".apk">
          <el-button size="default">点击上传</el-button>
          <template #tip>
            <span class="el-upload__tip"> 请上传安卓版本安装包，文件后缀名是apk</span>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="" class="FileDiv">
        <div v-show="androidFileEndResult" class="el-upload-list__item">
          <div class="el-upload-list__item-info">
            <a class="el-upload-list__item-name">
              <icon name="el-icon-document" />
              <span class="el-upload-list__item-file-name">{{ androidFileEndResult?.androidName }}</span>
            </a>
          </div>
          <icon name="el-icon-close el-icon--close" @click="androidFileEndResult = undefined" />
        </div>
      </el-form-item>
      <el-form-item label="附件" prop="file">
        <el-upload action="" :auto-upload="false" multiple :file-list="fileList" :on-change="onChange"
          :on-remove="onRemove">
          <el-button size="default">点击上传</el-button>
          <template #tip>
            <span class="el-upload__tip"> 请选择附件</span>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="" class="FileDiv">
        <div v-for="(item, index) in successFile" :key="index" class="el-upload-list__item">
          <div class="el-upload-list__item-info">
            <a class="el-upload-list__item-name">
              <icon name="el-icon-document" />
              <span class="el-upload-list__item-file-name">{{ item.name }}</span>
            </a>
          </div>
          <icon name="el-icon-close el-icon--close" @click="successFile.splice(index, 1)" />
        </div>
      </el-form-item>

      <!-- <el-form-item label="" prop="uploadFlag" class="flagDiv"
        v-if='Object.keys(fileList).length > 0 || successFile.length > 0'>
        <el-checkbox v-model="formData.uploadFlag">允许下载公告附件</el-checkbox>
      </el-form-item> -->
      <!-- <el-form-item label="公告类型" prop="type">
        <el-select v-model="formData.type" clearable placeholder="请选择公告类型">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_NOTICE_TYPE)" :key="parseInt(dict.value as any)"
            :label="dict.label" :value="parseInt(dict.value as any)" />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" clearable placeholder="请选择状态">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="parseInt(dict.value as any)"
            :label="dict.label" :value="parseInt(dict.value as any)" />
        </el-select>
      </el-form-item> -->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输备注" type="textarea" />
      </el-form-item>

      <!-- <el-form-item label="高级设置">
        <div style="margin-top: -4px;">
          <el-checkbox v-model="checkedDING" label="发 BAN 通知（发送 BAN 消息，重要通知确保对方收到）" size="large" />
        </div>
        <div style="margin-top: -8px;">
          <el-checkbox v-model="topFlag" label="设为置顶公告（可将重要公告置顶，始终处于列表顶部）" size="large" />
        </div>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <div>
        <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
      </div>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import * as FileApi from '@/api/infra/file'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import * as VersionApi from '@/api/system/versionInformation'
import { Editor } from '@/components/Editor'
import { getAccessToken, getTenantId } from '@/utils/auth'
import { useUpload } from '@/components/UploadFile/src/useUpload'
defineOptions({ name: 'SystemNoticeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const checkedDING = ref(false)//ban推送
// const topFlag = ref(false) //公告置顶
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formLoadingText = ref('') // 加载文案
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: '',
  type: undefined,
  content: '',
  versionNo: '',
  // status: CommonStatusEnum.ENABLE,
  remark: '',
  uploadFlag: false,
  coverPic : '',
  updatePic :  '',
  fileList: [],
  androidUrl: '',
  androidSize: '',
  androidName: ''
  // banFlag: 0,
  // topFlag: 0,
})
const formRules = reactive({
  title: [{ required: true, message: '版本标题不能为空', trigger: 'blur' }],
  // type: [{ required: true, message: '版本类型不能为空', trigger: 'change' }],
  // status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  content: [{ required: true, message: '版本内容不能为空', trigger: 'blur' }],
  versionNo: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
  coverPic: [{ required: true, message: '请上传封面', trigger: 'change' }],
  updatePic: [{ required: true, message: '请上传更新图片', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref
const successFile = ref([])
const androidFileList = ref([]) //el-upload绑定的androidFile
const androidFileEndResult = ref<{
  androidUrl: string, 
  androidSize: string, 
  androidName: string
} | undefined>() //最终提交的androidFileObject
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  checkedDING.value = false
  // topFlag.value = false
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await VersionApi.getVersion(id)
      console.log( formData.value,' formData.value');
      
      // checkedDING.value = formData.value.banFlag == 0 ? false : true
      // topFlag.value = formData.value.topFlag == 0 ? false : true
      if (formData.value.androidUrl) {
        androidFileEndResult.value = {
          androidUrl: formData.value.androidUrl,
          androidSize: formData.value.androidSize,
          androidName: formData.value.androidName        
        }
      }
      successFile.value = formData.value.fileList ? formData.value.fileList : []
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const fileList = ref([])//el-upload绑定的fileList
const endResult = ref(<any>[])//最终提交的fileList
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  if (fileList.value.length > 0 || androidFileList.value.length > 0) {
    formLoadingText.value = '附件上传中，这可能会花费较多的时间，请勿关闭窗口';
    const uploadQueue = <any>[]
    androidFileList.value.forEach((file: any) => {
      uploadQueue.push(
        FileApi.updateFile({ file: file.raw, sizeFlag: true })
      )
    })
    fileList.value.forEach((file: any) => {
      uploadQueue.push(
        FileApi.updateFile({ file: file.raw })
      )
    })
    Promise.all(uploadQueue).then(async res => {
      endResult.value = <any>[]
      let isFault = false;
      res.forEach((item, index) => {
        if (item.code === 0) {
          if (index == 0) {
            androidFileEndResult.value = {
              androidUrl: item.data.url,
              androidSize: item.data.sizeStr,
              androidName: item.data.name
            }
          } else {
            endResult.value.push(item.data)
          }
        } else {
          isFault = true
        }
      })
      if (isFault) {
        ElMessage.error("附件上传失败，请重新上传")
        formLoadingText.value = ''
        formLoading.value = false
        return
      }
      submitAll()
    }).catch(() => {
      ElMessage.error("附件上传失败，请重新上传")
      formLoadingText.value = ''
      formLoading.value = false
    })
  } else {
    formLoadingText.value = ''
    submitAll()
  }
}

const submitAll = async () => {
  try {
    // formData.value.banFlag = checkedDING.value ? 1 : 0
    // formData.value.topFlag = topFlag.value ? 1 : 0
    const data = formData.value as unknown as VersionApi.VersionVO
    const newArr = <any>[]
    if (successFile.value.length > 0) {
      successFile.value.forEach((item: any) => {
        newArr.push(item.url)
      })
    }
    newArr.push(...endResult.value)
    data.filePaths = newArr.length > 0 ? newArr.join(',') : ''
    data.androidUrl = androidFileEndResult.value?.androidUrl ?? ''
    data.androidSize = androidFileEndResult.value?.androidSize ?? ''
    data.androidName = androidFileEndResult.value?.androidName ?? ''
    console.log(data, 'submit-data')
    // return
    if (formType.value === 'create') {
      await VersionApi.createVersion(data)
      message.success(t('common.createSuccess'))
    } else {
      await VersionApi.updateVersion(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    emit('success') // 发送操作成功的事件
  } finally {
    formLoading.value = false
  }
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: '',
    type: undefined,
    content: '',
    versionNo: '',
    // status: CommonStatusEnum.ENABLE,
    remark: '',
    uploadFlag: false,
    // topFlag: 0
    coverPic : '',
    updatePic :  '',
    fileList: [],
    androidUrl: '',
    androidSize: '',
    androidName: ''
  }
  formRef.value?.resetFields()
  androidFileList.value = []
  androidFileEndResult.value = undefined
  fileList.value = []
  successFile.value = []
  endResult.value = []
}

const onAndroidUploadChange = (_: any, list: any) => {
  androidFileList.value = list
}

const onAndroidUploadRemove = (_: any, list: any) => {
  androidFileList.value = list
}

const onAndroidUploadExceed = () => {
  message.error('安装包文件仅能上传一个')
}

const onRemove = (file, list) => {
  fileList.value = list
}
const onChange = (file, list) => {
  fileList.value = list
}

</script>
<style lang="less" scoped>
.isEditor {
  width: 100%;
  // min-height: 300px;
}

.el-upload__tip {
  color: #909399;
  margin-left: 20px;
}

:deep(.el-form-item__content>div) {
  width: 100%;
}

.flagDiv {
  margin-top: -10px;
}

:deep(.el-upload-list__item) {
  border: 1px solid #dcdfe6;
}

:deep(.el-upload-list__item:hover) {
  background: #fff;
}

.FileDiv {
  margin-top: -18px;
}
</style>
