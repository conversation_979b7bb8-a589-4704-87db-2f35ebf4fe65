<template>
  <div class="top">
    <el-table v-loading="loading" :data="dataList">
      <el-table-column label="企业名称" prop="companyName" width="150" />
      <el-table-column label="罚单日期" prop="ticketDate" width="150" />
      <el-table-column label="违规人员部门" prop="deptName" width="150" />
      <el-table-column label="违规人员" prop="userName" width="150" />
      <el-table-column label="扣款金额(元)" prop="money" width="150" />
      <el-table-column label="扣款分值" prop="score" width="150" />
      <el-table-column label="扣款原因" prop="reason" width="150" />
      <el-table-column label="备注" prop="remark" width="150" />
      <el-table-column label="状态" prop="ticketStatus" width="150">
        <template #default="scope">
          <el-tag size="large" :type="scope.row.ticketStatus === 0 ? 'danger' : 'primary'">
            {{ scope.row.ticketStatus == 0 ? '失败' : '成功' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="失败原因" prop="badReason"  />

      <!-- <el-table-column
        :label="item"
        align="left"
        v-for="(item, index) in dataList1"
        :key="index"
        :prop="item"
      ></el-table-column> -->
    </el-table>
  </div>

  <div class="footerSubmit" :style="{ left: isCollapse ? '64px' : '200px' }">
    <el-button class="auditBtn" @click="reUpload">重新上传</el-button>
    <el-button type="primary" class="auditBtn" @click="nextStep">发送罚单</el-button>
  </div>
</template>
<script lang="ts" setup>
// import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { getUserProfile } from '@/api/system/user/profile'
import { propTypes } from '@/utils/propTypes'
import { Search, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as ticketApi from '@/api/system/penaltyTicket'

//
import dayjs from 'dayjs'
import type { DropdownInstance, ElMessageBox } from 'element-plus'
defineOptions({ name: 'orgstructure' })
import { couldStartTrivia, factory } from 'typescript'
import { includes } from 'lodash-es'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(false) // 列表的加载中
const props = defineProps({
  valId: propTypes.string.def('')
})
const dataList = ref([
  // {
  //   nickname: '哈哈'
  // }
])
const dataList1 = ref([])

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  username: ''
})

const emit = defineEmits(['success', 'reUpload']) // 定义 success 事件，用于操作成功后的回调
// 下一步
const nextStep = () => {
  emit('success')
}
// 重新上传
const reUpload = () => {
  emit('reUpload', '第二步')
}
// 发送
const send = () => {}
// 编辑
const edit = () => {}
// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const data = await ticketApi.previewViolationTicket()
    console.log(data, 'sadassss')
    // let newArry = []
    // for (let k in data[0]) {
    //   if (!k.includes('salary')) {
    //     newArry.push(k)
    //   }
    // }
    dataList.value = data.list
    // dataList1.value = newArry
  } finally {
    loading.value = false
  }
}
// 获取用户个人登录信息
// const getUserInfo = async () => {
//   const users = await getUserProfile()
//   userInfo.value = users
// }

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
  console.log(windowWidth)
}

/** 初始化 */
onMounted(() => {
  getList()

  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.top {
  margin-top: 40px;
  position: relative;
}

.isCBox-else {
  left: 0;
}
.footerSubmit {
  // position: fixed;
  // bottom: 0;
  // left: 220px;
  // right: 0;
  // height: 60px;
  // box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  // display: flex;
  // align-items: center;
  // justify-content: center;
  // background: #fff;
  // z-index: 5;

  //
  // text-align: center;
  // margin-top: 110px;
  // padding: 5px 0;
  // box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  // margin-bottom: -19px;
  //

  position: fixed;
  bottom: 0;
  // left: 220px;
  right: 0;
  height: 60px;
  box-shadow: 0px -1px 4px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  z-index: 5;
}
</style>
