<template>
  <!-- <img
        class="moxin"
        src="https://img.alicdn.com/imgextra/i2/O1CN01NvskDE1gchaywYE8u_!!6000000004163-2-tps-406-840.png"
      /> -->
  <div class="preview-entry">
    <div class="background-img">
      <div class="zitiao">{{ billDataRespVOList.name }}</div>
      <img class="moxin" src="@/assets/imgs/moxin.png" alt="" />
    </div>

    <div class="preview">
      <div class="processBox">
        <div class="layer"></div>
        <div class="weitu">
          <img src="@/assets/imgs/weitu.png" alt="" />
          <div class="us"
            ><span>{{ billDataRespVOList.empName }}，{{ billDataRespVOList.solicitude }}</span>
          </div>
          <div class="us1">
            <div>{{ billDataRespVOList.paySalary }}</div>
            <div>实发金额</div>
          </div>
        </div>
        <div class="tishiF" v-if="billDataRespVOList.warmTipFlag==0">
          <div class="tishi">
            <div>温馨提示</div>
            <!-- <div>工资条属于敏感信息，请注意保密</div> -->
            <div>{{billDataRespVOList.warmTip}}</div>
          </div>
        </div>

        <div
          style="padding: 0 6px"
          v-for="(item, index) in billDataRespVOList.billDataRespVOList"
          :key="index"
        >
          <div class="boxFor" v-if="!item.hidden">
            <div>{{ item.itemName }}</div>
            <div>{{ item.itemValue }}</div>
          </div>
        </div>

        <div v-if="billDataRespVOList.confirmStatus == 0">
          <el-divider>签字</el-divider>
          <div class="sign">
            <img :src="billDataRespVOList.sign" />
          </div>
        </div>
      </div>
    </div>
    <div class="fotr">
      <el-button class="auditBtn">对工资有疑问</el-button>
      <el-button type="primary" class="auditBtn">确认无误</el-button>
    </div>
  </div>

  <!-- ..... -->
  <!-- <div class="newPreview-entry">
    <div class="zitiao">{{ billDataRespVOList.name }}</div>
    <img class="moxin" src="@/assets/imgs/moxin.png" alt="" />
    <img src="@/assets/imgs/weitu.png" alt="" class="newWeitu" />
    <div class="us"
      ><span>{{ billDataRespVOList.empName }}，{{ billDataRespVOList.solicitude }}</span>
    </div>
    <div class="us1">
      <div>{{ billDataRespVOList.paySalary }}</div>
      <div>实发金额</div>
    </div>
    <div class="tishi">
      <div>温馨提示</div>
      <div>工资条属于敏感信息，请注意保密</div>
    </div>
  </div> -->
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { propTypes } from '@/utils/propTypes'
import { getUserProfile } from '@/api/system/user/profile'
import { Search, CaretBottom, ArrowRight } from '@element-plus/icons-vue'
import { tr } from 'element-plus/es/locale'
import { useAppStore } from '@/store/modules/app'
import * as payslipApi from '@/api/system/payslip'

import dayjs from 'dayjs'
import type { DropdownInstance, ElMessageBox } from 'element-plus'
defineOptions({ name: 'orgstructure' })
import { couldStartTrivia, factory } from 'typescript'
const props = defineProps({
  previewPhoneID: propTypes.number
})
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const billDataRespVOList = ref([])

const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

// 手机模型预览数据
const previewSalaryBill = async (id) => {
  const data = await payslipApi.previewSalaryBill({ id })
  console.log(data, 'fsd')
  // billDataRespVOList.value = data.billDataRespVOList
  billDataRespVOList.value = data
}

// 监听折叠面板
const appStore = useAppStore()
const collapse = computed(() => appStore.getCollapse)
const isCollapse = computed(() => appStore.getCollapse)
watch(
  () => collapse.value,
  (newPath, oldPath) => {
    isCollapse.value = newPath
  }
)
watch(
  props,
  () => {
    if (props.previewPhoneID) {
      previewSalaryBill(props.previewPhoneID)
    }
  },
  {
    immediate: true, // 在监听开始时立即执行
    deep: true // 深度监听
  }
)
// 监听浏览器宽度
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

/** 初始化 */
onMounted(() => {
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.newPreview-entry {
  position: relative;
}
.zitiao {
  position: absolute;
  top: 51px;
  left: 96px;
  font-weight: bold;
  font-size: 14px;
  color: #ffffff;
  //
  // position: absolute;
  // top: 72px;
  // left: 145px;
}
.newWeitu {
  position: absolute;
  left: 18.5%;
  top: 16%;
}

.preview-entry {
  position: relative;
  // width: 203px;
  width: 302px;
  // height: 100%;
  margin-left: 31px;
  margin-top: 30px;

  // width: 302px;
  // height: 584px;
  .background-img {
    position: absolute;
    width: 100%;
    z-index: 1;
    // height: 100%;
    pointer-events: none;
  }
  .moxin {
    width: 100%;
    height: 100%;
  }
}
.preview {
  background-color: #f6f6f6;
  border-radius: 0 0 42px 42px;
  overflow-y: scroll;
  // position: absolute;
  width: 100%;
  // height: 100%;
  height: 530px;
}
.processBox {
  position: relative;
  padding-top: 60px;
  padding-left: 15px;
}

.layer {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 12;
}
.weitu {
  margin: 40px 0 0 5px;
  position: relative;
}
.us {
  position: absolute;
  top: 20px;
  // top: 149px;
  left: 50%;
  transform: translate(-50%);
  font-weight: 500;
  font-size: 10px;
  color: #ffffff;
  > span:nth-of-type(1) {
    display: block;
    width: 100px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
    text-align: center;
  }
}
.us1 {
  position: absolute;
  top: 52px;
  // top: 180px;
  left: 50%;
  transform: translate(-50%);
  > div:nth-of-type(1) {
    font-weight: bold;
    font-size: 28px;
    color: #ffffff;
    text-align: center;
  }
  > div:nth-of-type(2) {
    font-weight: 500;
    font-size: 10px;
    color: #ffffff;
    text-align: center;
  }
}
.tishiF {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
.tishi {
  width: 242px;
  height: 62px;
  background: #fffbe6;
  border-radius: 6px;
  // margin: 10px auto 0 auto;
  padding: 7px 8px 0 8px;
  box-sizing: border-box;
  > div:nth-of-type(1) {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
  }
  > div:nth-of-type(2) {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
    margin-top: 8px;
  }
}
.boxFor {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  background: #fff;
  margin-top: 10px;
  height: 34px;
  line-height: 34px;
  > div {
    font-weight: 400;
    font-size: 12px;
    color: #606266;
  }
  > div:nth-of-type(1) {
    margin-left: 8px;
  }
  > div:nth-of-type(2) {
    margin-right: 8px;
  }
}
.titleP {
  margin: 0;
  padding: 0 0.625rem 0.625rem 1rem;
  color: #545456;
  font-size: 10px;
}
.fotr {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}
</style>
