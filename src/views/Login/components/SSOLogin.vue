<template>
  <div class="center-view" style="background: #F3F4F7;height:100vh;">
    <el-card v-show="ssoVisible" class="oaCard" style="width:640px;margin-top:-15vh;">
 
     <!-- 应用名 -->
     <h2 class="title">
      {{ getFormTitle }}
    </h2>
    <img src="@/assets/imgs/logoBlue.png" class=" flex mb-54px m-auto text-center  h-60px w60px" />
    <el-tabs v-model="defaultName" class="dialogTab">
      <el-tab-pane :label="client.name" name="defaultName" />
    </el-tabs>
    <div>
      <el-form :model="formData" class="login-form">
        <!-- 授权范围的选择 -->
        <p class="tipP">此第三方应用请求获得以下权限：</p>
        <el-form-item prop="scopes">
          <el-checkbox-group v-model="formData.scopes">
            <el-checkbox v-for="scope in queryParams.scopes" :key="scope" :label="scope">
              {{ formatScope(scope) }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <!-- 下方的登录按钮 -->
        <el-form-item class="btnClass">
          <el-button @click.prevent="handleAuthorize(false)">拒绝</el-button>
          <el-button :loading="formLoading" type="primary" @click.prevent="handleAuthorize(true)">
            <span v-if="!formLoading">同意授权</span>
            <span v-else>授 权 中...</span>
          </el-button>
        </el-form-item>
      </el-form>
    </div>
 
 
  </el-card>
  </div>
</template>
<script lang="ts" setup>
import { setCustomToken, getAccessToken } from '@/utils/auth'
import * as OAuth2Api from '@/api/login/oauth2'
import { LoginStateEnum, useLoginState } from './useLogin'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

defineOptions({ name: 'SSOLogin' })

const route = useRoute() // 路由
const { currentRoute } = useRouter() // 路由
const { getLoginState, setLoginState } = useLoginState()

const client = ref({
  // 客户端信息
  name: '',
  logo: ''
})
const defaultName = ref('defaultName')
const { t } = useI18n()
const message = useMessage() // 消息弹窗
const getFormTitle = computed(() => {
  const titleObj = {
    [LoginStateEnum.RESET_PASSWORD]: t('sys.login.forgetFormTitle'),
    [LoginStateEnum.LOGIN]: t('sys.login.signInFormTitle'),
    [LoginStateEnum.REGISTER]: t('sys.login.signUpFormTitle'),
    [LoginStateEnum.MOBILE]: t('sys.login.mobileSignInFormTitle'),
    [LoginStateEnum.QR_CODE]: t('sys.login.qrSignInFormTitle'),
    [LoginStateEnum.SSO]: t('sys.login.ssoFormTitle')
  }
  return titleObj[unref(getLoginState)]
})

interface queryType {
  responseType: string
  clientId: string
  redirectUri: string
  state: string
  scopes: string[]
}
const queryParams = reactive<queryType>({
  // URL 上的 client_id、scope 等参数
  responseType: '',
  clientId: '',
  redirectUri: '',
  state: '',
  scopes: [] // 优先从 query 参数获取；如果未传递，从后端获取
})
const ssoVisible = computed(() => unref(getLoginState) === LoginStateEnum.SSO) // 是否展示 SSO 登录的表单
interface formType {
  scopes: string[]
}
const formData = reactive<formType>({
  scopes: [] // 已选中的 scope 数组
})
const formLoading = ref(false) // 表单是否提交中

/** 初始化授权信息 */
const init = async () => {
  // 防止在没有登录的情况下循环弹窗
  if (typeof route.query.client_id === 'undefined') return
  // 解析参数
  // 例如说【自动授权不通过】：client_id=default&redirect_uri=https%3A%2F%2Fwww.iocoder.cn&response_type=code&scope=user.read%20user.write
  // 例如说【自动授权通过】：client_id=default&redirect_uri=https%3A%2F%2Fwww.iocoder.cn&response_type=code&scope=user.read
  queryParams.responseType = route.query.response_type as string
  queryParams.clientId = route.query.client_id as string
  queryParams.redirectUri = route.query.redirect_uri as string
  queryParams.state = route.query.state as string
  if (route.query.scope) {
    queryParams.scopes = (route.query.scope as string).split(' ')
  }

  // 如果有 scope 参数，先执行一次自动授权，看看是否之前都授权过了。
  if (queryParams.scopes.length > 0) {
    const data = await doAuthorize(true, queryParams.scopes, [])
    if (data) {
      window.location.href = data
      return
    }
  }

  // 获取授权页的基本信息
  const data = await OAuth2Api.getAuthorize(queryParams.clientId)
  client.value = data.client
  // 解析 scope
  let scopes
  // 1.1 如果 params.scope 非空，则过滤下返回的 scopes
  if (queryParams.scopes.length > 0) {
    scopes = []
    for (const item of data.scopes) {
      if (queryParams.scopes.indexOf(item.key) >= 0) {
        scopes.push(item)
      }
    }
    // 1.2 如果 params.scope 为空，则使用返回的 scopes 设置它
  } else {
    scopes = data.scopes
    for (const item of scopes) {
      queryParams.scopes.push(item.key)
    }
  }
  console.log(scopes, 'scopes')
  // 生成已选中的 checkedScopes
  for (const item of scopes) {
    // if (item.value) {
    formData.scopes.push(item.key)//不管状态 全部默认勾选
    // }
  }
  console.log(formData, 'formData')
}

const addIsBanban =  (url) => {
    // 使用正则表达式查找 &accessToken=
    const pattern = /(&accessToken=)/;
    // 如果找到，则在前面插入 &isBanban=true
    const newUrl = url.replace(pattern, '&isBanban=true$1');
    return newUrl;
} 

/** 处理授权的提交 */
const handleAuthorize = async (approved) => {
  // 计算 checkedScopes + uncheckedScopes
  let checkedScopes
  let uncheckedScopes
  if (approved) {
    // 同意授权，按照用户的选择
    if (formData.scopes.length != queryParams.scopes.length) return message.warning('请勾选同意项')
    checkedScopes = formData.scopes
    uncheckedScopes = queryParams.scopes.filter((item) => checkedScopes.indexOf(item) === -1)
  } else {
    // 拒绝，则都是取消
    checkedScopes = []
    uncheckedScopes = queryParams.scopes
  }
  // 提交授权的请求
  formLoading.value = true
  try {
    const data = await doAuthorize(false, checkedScopes, uncheckedScopes)
    if (!data) {
      return
    }
   

    var newUrl = addIsBanban(data);
    console.log('newUrl=',newUrl)
    window.location.href = newUrl
  } finally {
    formLoading.value = false
  }
}

/** 调用授权 API 接口 */
const doAuthorize = (autoApprove, checkedScopes, uncheckedScopes) => {
  return OAuth2Api.authorize(
    queryParams.responseType,
    queryParams.clientId,
    queryParams.redirectUri,
    queryParams.state,
    autoApprove,
    checkedScopes,
    uncheckedScopes
  )
}

/** 格式化 scope 文本 */
const formatScope = (scope) => {
  // 格式化 scope 授权范围，方便用户理解。
  // 这里仅仅是一个 demo，可以考虑录入到字典数据中，例如说字典类型 "system_oauth2_scope"，它的每个 scope 都是一条字典数据。
  switch (scope) {
    case 'user.read':
      return '访问你的个人信息'
    case 'user.write':
      return '修改你的个人信息'
    default:
      return scope
  }
}

/** 监听当前路由为 SSOLogin 时，进行数据的初始化 */
watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    if (route.name === 'SSOLogin') {
      setLoginState(LoginStateEnum.SSO)
      init()
    }
  },
  { immediate: true }
)


// const handleMessage = (event: MessageEvent) => {
//   console.log('handleMessage')
//   console.log(event.origin, 'event.origin')
//   console.log(event.data, 'event.data')
//   // if (event.origin !== 'http://localhost') {
//   //   return
//   // }

//   // 处理消息
//   const data = event.data;
//   if (data.type === 'urlParams') {
//     console.log('Received params:', data.params)
//     setCustomToken({
//       refreshToken: data.params.refreshToken ? data.params.refreshToken : '',
//       accessToken: data.params.accessToken ? data.params.accessToken : '',
//     })
//     console.log(data.params.accessToken, 'data.params.accessToken')
//     console.log(getAccessToken())
//     init()
//   }
// }
// onMounted(() => {
//   window.addEventListener('message', handleMessage)
// })

// onUnmounted(() => {
//   window.removeEventListener('message', handleMessage)
// })
</script>
<style lang="less" scoped>
.oaCard {
  padding: 20px 80px;
  border: none !important;
}

.title {
  text-align: center;
  margin: 0;
  margin-bottom: 60px;
  font-size: 22px;
  color: #303133;
  font-weight: normal;
}

:deep(.dialogTab .el-tabs__nav) {
  margin-left: 0;
}

:deep(.dialogTab .el-tabs__item) {
  height: 32px;
  line-height: 32px;
  color: #3370FF;
  font-size: 16px;
}

:deep(.el-tabs__active-bar) {
  background: #3370FF;
}

.tipP {
  margin-top: 30px;
  font-size: 16px;
  color: #303133;
  line-height: 22px;
  font-weight: bold;
}

:deep(.el-checkbox) {
  display: flex;
  height: auto;
  margin-bottom: 14px;
}

:deep(.el-checkbox__inner) {
  border-radius: 50%;

}

:deep(.el-checkbox__inner:hover) {
  border-color: #3370FF;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #3370FF;
  border-color: #3370FF;
}

:deep(.el-checkbox__label) {
  color: #909399;
}

:deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
  font-weight: 400;
  font-size: 14px;
  color: #909399;
}

:deep(.el-checkbox-group) {
  margin-bottom: 50px;
}

.btnClass {
  display: flex;
  justify-content: space-between;
  margin-bottom: 60px;
  margin-top: 40px;

  .el-button {
    flex: 1;
    height: 40px;
    border-radius: 5px;
  }

  .el-button--primary {
    background: #3370FF;
    border: none;
  }
}
</style>