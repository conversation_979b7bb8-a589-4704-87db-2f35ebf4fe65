<template>
  <!-- 花名册 -->
  <el-card class="oaCard" v-if="isShow == 1">
    <el-row :gutter="20">
      <el-col :span="17" :xl="17" :lg="17" :md="24" :sm="24" :xs="24" v-loading="loadingLeft">
        <div class="empClass">
          <div class="totalNum">
            <span class="text">总数</span>
            <span class="num">{{ numTotal }}</span>
          </div>
          <div v-for="(item, index) in NumList" :key="index" class="forBox" :class="{ redColor: item.label == '无类型' }">
            <p class="p1">{{ item.label }}</p>
            <p class="p2">{{ item.value }}</p>
          </div>
        </div>
      </el-col>
      <el-col :span="7" :xl="7" :lg="7" :md="24" :sm="24" :xs="24" v-loading="loadingRight">
        <div class="empClass">
          <div v-for="(item, index) in statusList" :key="index" class="forBox">
            <p class="p1">{{ item.label }}</p>
            <p class="p2">{{ item.value }}</p>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
      <el-form-item label="用户姓名">
        <el-input v-model="searchParams.username" placeholder="请输入用户姓名" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="手机号码">
        <el-input v-model="searchParams.mobile" placeholder="请输入手机号码" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="入职时间">
        <el-date-picker v-model="searchParams.entry_time" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" icon="el-icon-search" v-hasPermi="['system:user-roster:search']">搜索</el-button>
        <el-button @click="resetQuery" icon="el-icon-refresh" v-hasPermi="['system:user-roster:refresh']">重置</el-button>
        <!-- <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:user-roster:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button> -->
        <!-- <el-button type="success" plain @click="handleExport" :loading="exportLoading"
          v-hasPermi="['system:user-roster:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" @row-click="rowClick">
      <el-table-column label="序号" type="index" align="center" width="120" />
      <el-table-column label="姓名" prop="username" />
      <el-table-column label="部门" prop="dept_name" />
      <el-table-column label="职位" prop="post_name" />
      <el-table-column label="入职时间" prop="entry_time" />
      <el-table-column label="员工类型" prop="emp_type" />
      <el-table-column label="手机号" prop="mobile">
        <template #default="scope">{{ hideNumber(scope.row.mobile) }}</template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button link type="primary" @click.stop="handleDetail(scope.row)"
            v-hasPermi="['system:user-roster:detail']">
            详情
          </el-button>
          <!-- <el-button link type="danger" @click.stop="handleDelete(scope.row.id)"
            v-hasPermi="['system:user-roster:delete']">
            删除
          </el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </el-card>

  <!-- 详情 -->
  <detail ref="detailRef" v-if="isShow == 2" @close="handleClose" />
</template>

<script setup lang="ts">
// import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { UserRosterApi } from '@/api/hrManage/userRoster'
import detail from './userDetail.vue'
/** 花名册 列表 */
defineOptions({ name: 'UserRoster' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const isShow = ref(1)//默认展示1
const loading = ref(true) // 列表的加载中
const list = ref<[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<{
  pageNo: any,
  pageSize: any,
  // sqlWhereConditionList: any
}>({
  pageNo: 1,
  pageSize: 10,
  // sqlWhereConditionList: []
})
const searchParams = ref<{
  username: any,
  mobile: any,
  entry_time: any
}>({
  username: '',
  mobile: '',
  entry_time: []
})
const queryFormRef = ref() // 搜索条件
const exportLoading = ref(false) // 导出的加载中

// 状态值
const statusList = ref<any[]>([])//状态列表
const loadingLeft = ref(false)
const getStatus = async () => {
  statusList.value = []
  loadingLeft.value = true
  try {
    const status = await UserRosterApi.getEmpStatus()
    for (const item in status) {
      statusList.value.push({
        label: item,
        value: status[item]
      })
    }
  } finally {
    loadingLeft.value = false
  }
}
const loadingRight = ref(false)
const NumList = ref<any[]>([])//数量列表
const numTotal = ref(0)
const getNmu = async () => {
  NumList.value = []
  numTotal.value = 0
  loadingRight.value = true
  try {
    const num = await UserRosterApi.getEmpNum()
    for (const item in num) {
      if (item == 'total') {
        numTotal.value = num[item]
      } else {
        NumList.value.push({
          label: item,
          value: num[item]
        })
      }
    }
    console.log(NumList.value, 'NumList.value')
  } finally {
    loadingRight.value = false
  }
}
// 列表
const arr = ref<any[]>([])
const getList = async () => {
  loading.value = true
  try {
    arr.value = []
    for (let key in searchParams.value) {
      const value = searchParams.value[key]
      if (key == 'entry_time' && value.length > 0) {
        console.log(value)
        arr.value.push(
          {
            fieldCode: key,
            fieldValue: value[0],
            condition: 6
          }, {
          fieldCode: key,
          fieldValue: value[1],
          condition: 4
        },
        )
      }
      if (key != 'entry_time') {
        if (value.trim()) {
          arr.value.push({
            fieldCode: key,
            fieldValue: value.trim(),
            condition: 7
          })
        }
      }
    }
    const data = await UserRosterApi.getUserRosterPage({
      ...queryParams,
      sqlWhereConditionList: arr.value.length > 0 ? arr.value : undefined,
      sqlOrderBy: {
        fieldCode: "instance_id",
        desc: true
      },
      // tableId: 2
    })
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
  getStatus()
  getNmu()
}

// 重置
const resetQuery = () => {
  queryFormRef.value.resetFields()
  searchParams.value = {
    username: '',
    mobile: '',
    entry_time: []
  }
  handleQuery()
}

// 删除
const handleDelete = async (id: number) => {
  try {
    await message.delConfirm()
    await UserRosterApi.deleteUserRoster(id)
    message.success(t('common.delSuccess'))
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserRosterApi.exportUserRoster(queryParams)
    download.excel(data, '花名册.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
// 行点击事件
const rowClick = (e) => {
  // handleDetail(e)
}
// 手机号中间四位隐藏
const hideNumber = (e) => {
  const regex = /(\d{3})\d{4}(\d{4})/;
  const maskedNumber = e.replace(regex, '$1****$2');
  return maskedNumber
}

// 查看详情
const detailRef = ref()
const handleDetail = (e) => {
  isShow.value = 2
  setTimeout(() => {
    detailRef.value.open(e)
  }, 100)

}
// 关闭子页面
const handleClose = () => {
  isShow.value = 1
}
/** 初始化 **/
onMounted(() => {
  getList()
  getStatus()
  getNmu()
})
</script>
<style lang="less" scoped>
.el-table {
  margin-top: 20px;
}

.empClass {
  border: 1px solid #e9e9ea;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  min-height: 84px;

  .totalNum {
    color: #0089ff;
    border-right: 1px solid #e7e8eb;
    font-size: 16px;
    line-height: 30px;
    padding-top: 30px;
    text-align: center;
    width: 160px;

    .text {
      margin-right: 6px;
    }
  }

  .forBox {
    width: 145px;
    text-align: center;
    font-size: 14px;
    padding: 15px 0 10px;
    box-sizing: border-box;

    p {
      margin: 0;
      line-height: 30px;
      padding: 0;
    }

    .p1 {}
  }
}
</style>