<template>
  <div>
    <el-dialog
      v-model="dialog"
      title="审核新成员"
      :before-close="drawerClose"
      top="10vh"
      class="custom-dialog"
      width="700px"
    >
      <div v-for="item in items" :key="item.id">
        <div class="center-align" style="margin-bottom: 10px">
          <div style="width: 180px"> {{ item.fieldName }} :</div>
          <template v-if="item.fieldName != '模版编号'">
            <el-input v-model="item.value" style="width: 240px" placeholder="请输入内容" />
          </template>
          <template v-else>
            <el-select
              style="width: 240px"
              v-model="item.value" placeholder="请选择">
              <el-option
                v-for="option in item.options"
                :key="option.templateNo"
                :label="option.templateName"
                :value="option.templateNo"
              >
              </el-option>
            </el-select>
          </template>
        </div>
      </div>
      <div class="radioBox">
        <span>审核结果：</span>
        <el-radio-group v-model="radio">
          <el-radio :label="0">同意</el-radio>
          <el-radio :label="1">拒绝</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <el-button @click="drawerClose">取消</el-button>
        <el-button type="primary" @click="submit" :loading="loading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { approve, completeList, getContractTemplateList } from '@/api/hrManage/newStaff'
import { ref } from 'vue'

const props = defineProps({
  id: {
    type: Number,
    default: ''
  },
  mobile: {
    type: String,
    default: ''
  },
  username: {
    type: String,
    default: ''
  }
})
const loading = ref(false) //防连点
const dialog = ref(false) //弹窗默认false
const radio = ref(0) //审核状态
const items = ref([]) // 使用 ref 创建响应式变量
const open = () => {
  loading.value = false
  radio.value = 0
  dialog.value = true
  completeItems()
}
function drawerClose() {
  dialog.value = false
}

const submit = async () => {
  loading.value = true
  try {
    console.log('items=', items.value)
    // 封装 customDataList 数组
    const customDataList = items.value.map((item) => ({
      fieldId: item.id || 0,
      fieldKey: item.fieldCode || '',
      fieldName: item.fieldName,
      fieldType: item.fieldType || '',
      isJson: item.props?.length > 0 || false,
      // 输入框中的值
      fieldValue: item.value
    }))
    const res = await approve({ id: props.id, approveStatus: radio.value, customDataList })
    if (res.code == 0 || res.code == 601) {
      emit('close')
      drawerClose()
    }
    if (res.code == 0) {
      return ElNotification.success('操作成功')
    } else {
      return ElNotification.error(res.msg)
    }
  } finally {
    loading.value = false
  }
}

const emit = defineEmits(['close'])
defineExpose({
  open,
  drawerClose
})

const setValue = (item) => {
  switch (item.fieldCode) {
    case 'mobile':
      return props.mobile

    case 'username':
      return props.username
    default:
      break
  }

  return ''
}

const completeItems = async () => {
  try {
    const cpls = await completeList()

    // 遍历返回的 cpls，确保每个项都包含一个 value 属性
    items.value = await Promise.all(
      cpls.map(async (item) => {
        // 如果 fieldName 是 '模版编号'，则调用 getContractTemplateList 接口
        if (item.fieldName === '模版编号') {
          try {
            item.options = await getContractTemplateList() // 将接口返回的数据赋值给 value
          } catch (error) {
            console.error('获取模板列表失败：', error)
            item.options = [] // 如果请求失败，设置默认值
          }
        } else {
          // 其他情况调用 setValue 方法
          item.value = setValue(item)
        }
        return item
      })
    )

    console.log(items.value)
  } catch (error) {
    console.error('Error fetching items:', error)
  }
}
</script>
<style lang="less" scoped>
.radioBox {
  line-height: 32px;
  display: flex;
  margin-bottom: 20px;
  span {
    width: 80px;
    display: inline-block;
  }
}
:deep(.el-checkbox__inner) {
  width: 18px;
  height: 18px;
}

:deep(.el-checkbox__inner::after) {
  left: 6px;
  top: 3px;
}

:deep(.el-radio__inner::after) {
  width: 6px;
  height: 6px;
}

:deep(.el-radio__inner) {
  width: 18px;
  height: 18px;
}
</style>
