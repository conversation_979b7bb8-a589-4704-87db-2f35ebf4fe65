<template>
  <!-- 办理入职 弹窗 -->
  <div>
    <el-drawer title="办理入职" v-model="dialogVisible" class="custom-detail-header" :size="drawerSize">
      <el-tabs v-model="activeName" class="dialogTab tabCustom" v-loading='loading'>
        <el-tab-pane label="扫码入职" name="1" class="tabPane1">
          <el-alert title="员工扫码填写入职登记表，提交后自动进入待入职列表" type="warning" show-icon class="alert-custom" />
          <span class="title">员工APP扫码填写入职登记表</span>
          <div class="content-wrapper">
            <div class="qrcode">
              <div class="qrcode-box" ref="myElement">
                <p class="tip1">{{ tenantName }}</p>
                <p class="tip2">入职二维码</p>
                <p class="tip3">使用手机诺鑫办办扫码填表</p>
                <img :src='code' crossorigin="anonymous">
              </div>
              <div class="action-box">
                <div class="qrcode-input">
                  <el-input v-model="inputUrl" readonly>
                    <template #suffix>
                      <el-icon title="复制链接" @click="handleCopy" class="copy-hover">
                        <CopyDocument />
                      </el-icon>
                    </template>
                  </el-input>
                </div>
                <el-button class="qrcode-down" type="primary" @click="handleSave">下载二维码</el-button>
                <el-button class="qrcode-edit" @click="handleEdit">编辑入职登记表</el-button>
              </div>
            </div>
            <div class="preview-entry">
              <div class="background-img">
                <img src="https://img.alicdn.com/imgextra/i2/O1CN01NvskDE1gchaywYE8u_!!6000000004163-2-tps-406-840.png">
              </div>
              <div class="preview">
                <previewForm></previewForm>
              </div>
            </div>
          </div>
          <p class="seatP"></p>
        </el-tab-pane>
        <el-tab-pane label="手动添加" name="2">
          <el-alert title="可手动添加待入职员工，可邀请员工补充入职信息" type="warning" show-icon class="alert-custom" />
          <manualAdd @success="drawerClose" ref="manualAddRef"></manualAdd>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>
  </div>
</template>

<script setup type="ts">
import { getTenant } from '@/api/sys'
import { getTenantId } from '@/utils/auth'

import { EntryApi } from '@/api/hrManage/empManage'
import { qrcode } from '@/api/hrManage/newStaff'
import { CopyDocument } from '@element-plus/icons-vue'
import useClipboard from "vue-clipboard3";//复制
import html2canvas from 'html2canvas'
import { useRouter } from 'vue-router'
import previewForm from './previewForm.vue'
import manualAdd from './manualAdd.vue'
const { toClipboard } = useClipboard()

const router = useRouter()
const dialogVisible = ref(false)//弹窗属性
const activeName = ref('1')//tab选项
const loading = ref(false)
const manualAddRef = ref()//手动添加模块的表单ref
const drawerSize = ref('33%')//抽屉尺寸大小
const open = () => {
  dialogVisible.value = true
  activeName.value = '1'
  drawerSize.value = '33%'
  window.addEventListener('resize', handleResize)
  handleResize()
  getUrl()
  getTenantName()
  nextTick(() => {
    manualAddRef.value.open()
  })
}
// 关闭弹窗
function drawerClose(e) {
  dialogVisible.value = false
  if (e == 'success') {
    emit('success')
  }
  window.removeEventListener('resize', handleResize)
}
// 监听浏览器的窗口事件
const windowWidth = ref(window.innerWidth)
const handleResize = () => {
  windowWidth.value = window.innerWidth
  if (window.innerWidth > 1650) {
    drawerSize.value = '33%'
  }
  if (window.innerWidth <= 1650) {
    drawerSize.value = '45%'
  }
  if (window.innerWidth <= 1500) {
    drawerSize.value = '50%'
  }
  if (window.innerWidth < 1050) {
    drawerSize.value = '70%'
  }
  if (window.innerWidth < 700) {
    drawerSize.value = '95%'
  }
}

// 获取url
const inputUrl = ref('')
const getUrl = async () => {
  loading.value = true
  try {
    const res = await EntryApi.getEntryUrl()
    getCode(res)
    inputUrl.value = res
  } catch (e) {
    loading.value = false
  } finally {
    // loading.value = false
  }
}
// 二维码图片
const code = ref('')
const getCode = async (url) => {
  loading.value = true
  try {
    const res = await qrcode({ url: url })
    code.value = res
  } finally {
    loading.value = false
  }
}
// 复制链接
const handleCopy = async () => {
  try {
    await toClipboard(inputUrl.value)
    ElMessage.success("复制成功")
  } catch (e) {
    ElMessage.error(e)
  }
}
// 保存
const myElement = ref()//需要生成的div
const handleSave = async () => {
  const divToConvert = myElement.value
  try {
    const canvas = await html2canvas(divToConvert, {
      backgroundColor: '#ffffff',
      useCORS: true,
    })
    const dataURL = canvas.toDataURL('image/png')
    const downloadLink = document.createElement('a')
    downloadLink.href = dataURL;
    downloadLink.download = '入职二维码.png'
    downloadLink.click();
  } catch (error) {
    ElMessage.error(error)
  }
}
const handleEdit = () => {
  router.push('/hrManage/employeeDataSetting')
}

const tenantName = ref('')
const getTenantName = async () => {
  tenantName.value = JSON.parse(localStorage.getItem('currentList'))[0].name
}


const emit = defineEmits(['success'])
defineExpose({
  open
})
</script>
<style lang="less" scoped>
:deep(.el-drawer__body) {
  padding: 0 !important;
}

:deep(.tabCustom .el-tabs__item) {
  font-size: 14px !important;
}

.alert-custom {
  background-color: #e6f7ff;
  border: 1px solid #91d5ff;
  color: #333;
  padding: 8px 15px;
  margin-top: 20px;
  font-size: 14px;
  line-height: 1.5;

  :deep(.el-alert__icon) {
    color: #1890ff;
    font-size: 15px;
    line-height: normal;
    margin-top: -1px;
  }
}

.tabPane1 {
  .title {
    color: #303133;
    display: flex;
    justify-content: center;
    margin: 20px 0 35px;
  }

  .content-wrapper {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 500px;
    position: relative;
  }

  .qrcode {
    background-color: #f2f2f6;
    border: 1px solid #f2f2f6;
    border-radius: 6px;
    margin-right: 20px;
    width: 255px;
  }

  .qrcode-box {
    background-color: #fff;
    border-radius: 6px 6px 0 0;
    // height: 240px;
    padding: 16px;
    width: 255px;
    box-sizing: border-box;
    position: relative;
    padding-top: 90px;
    border: 1px solid #f2f2f6;
    padding-bottom: 20px;

    img {
      width: 100%;
      height: 100%;
    }

    p {
      margin: 0;
      text-align: center;
      position: absolute;
      top: 16px;
      left: 0;
      right: 0;
    }

    .tip1 {}

    .tip2 {
      font-size: 26px;
      font-weight: bold;
      margin-bottom: 20px;
      top: 40px;
    }
    .tip3{
      bottom: 10px;
      top:auto;
      font-size:12px;
      color:rgba(23, 26, 29, .4);
    }
  }

  .action-box {
    padding: 16px 16px 0;
  }

  .qrcode-input {
    margin-bottom: 10px;
    width: 100%;

    .copy-hover {
      cursor: pointer;
    }
  }

  .qrcode-down {
    margin-bottom: 10px;
    width: 100%;
  }

  .qrcode-edit {
    width: 100%;
    margin-left: 0;
  }

  .preview-entry {
    position: relative;
    width: 240px;
    height: 100%;

    .background-img {
      position: absolute;
      width: 100%;
      z-index: 1;
      height: 100%;
      pointer-events: none;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .preview {
      background-color: #f6f6f6;
      border-radius: 0 0 42px 42px;
      overflow-y: scroll;
      position: absolute;
      width: 100%;
      height: 100%;
    }
  }
}

.seatP {
  height: 40px;
}

.preview-entry::after {
  content: '上下滑动可预览入职登记表';
  position: absolute;
  bottom: -22px;
  right: 0;
  left: 0;
  margin: 0 auto;
  color: rgba(23, 26, 29, .4);
  font-size: 12px;
  text-align: center;
}
</style>