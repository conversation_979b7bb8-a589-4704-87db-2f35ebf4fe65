<template>
  <div>
    <el-card class='oaCard marginBottom' v-if="isShow == 1">
      <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true">
        <el-form-item label="表名称" prop="tableName">
          <el-input v-model="queryParams.tableName" placeholder="请输入表名称" clearable @keyup.enter="handleQuery"
            class="!w-240px" />
        </el-form-item>
        <el-form-item label="创建时间" prop="createTime">
          <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
            start-placeholder="开始日期" end-placeholder="结束日期"
            :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
        </el-form-item>
        <el-form-item>
          <el-button @click="handleQuery" icon="el-icon-search"  v-hasPermi="['system:custom-table:search']">搜索</el-button>
          <el-button @click="resetQuery" icon="el-icon-refresh"  v-hasPermi="['system:custom-table:reload']">重置</el-button>
          <el-button @click="openForm('create')" type="primary" plain icon="el-icon-plus"  v-hasPermi="['system:custom-table:add']">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loading" :data="list" @row-click="rowClick">
        <el-table-column label="序号" type="index" align="center" width="120" />
        <el-table-column label="表名称" prop="tableName" />
        <el-table-column label="表描述" prop="tableComment" />
        <el-table-column label="备注" prop="remark" />
        <el-table-column label="创建时间" prop="createTime" :formatter="dateFormatter" width="230px" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row.id, scope.row.tableName)"  v-hasPermi="['system:custom-table:detail']">
              详情
            </el-button>
            <el-button link type="primary" @click.stop="openForm('update', scope.row.id)"
              v-hasPermi="['system:custom-table:update']">
              编辑
            </el-button>
            <el-button link type="danger" @click.stop="handleDelete(scope.row.id)"
              v-hasPermi="['system:custom-table:delete']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </el-card>
    <abc ref="detailRef" v-show="isShow == 2" @close="close" />
    <!-- 表单弹窗：添加/修改 -->
    <CustomTableForm ref="formRef" @success="getList" />
  </div>
</template>
<script setup type="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { CustomTableApi } from '@/api/hrManage/customTable'
import CustomTableForm from './dialog/addTable.vue'
import abc from './detail.vue'

defineOptions({ name: 'CustomTable' })
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const loading = ref(true) // 列表的加载中
const isShow = ref(1)//默认展示1
const list = ref([])// 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  tableName: undefined,
  // tableComment: undefined,
  // remark: undefined,
  createTime: [],
})
const total = ref(0)
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const activeName = ref()//tab默认展示
const defaultId = ''
/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await CustomTableApi.getCustomTablePage(queryParams)
    list.value = data.list
    total.value = data.total
    activeName.value = 0
  } finally {
    loading.value = false
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}
/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 添加/编辑操作 */
const formRef = ref()
const openForm = (type, id) => {
  formRef.value.open(type, id)
}
/** 删除按钮操作 */
const handleDelete = async (id) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await CustomTableApi.deleteCustomTable(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

// 行点击事件
const rowClick = (e) => {
  // handleDetail(e.id,e.tableName)
}
// 查看详情
const detailRef = ref()
const handleDetail = (id, name) => {
  isShow.value = 2
  detailRef.value.open(id, name)
}
// 关闭详情
const close = () => {
  isShow.value = 1
}
onMounted(() => {
  getList()
})
</script>
<style lang="less" scoped>
.el-table {
  margin-top: 20px;
}
</style>