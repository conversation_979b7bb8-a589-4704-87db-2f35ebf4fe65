import request from '@/config/axios'

export const salaryBill = async (params) => {
  return await request.get({ url: '/system/salary-bill/page', params })
}

export const salaryBillUser = async (params) => {
  return await request.get({ url: '/system/salary-bill-user/page', params })
}
export const sendSalaryBill = async (params) => {
  return await request.get({ url: '/system/salary-bill-user/send', params })
}

export const previewSalaryBill = async (params) => {
  return await request.get({ url: '/system/salary-bill-user/preview', params })
}

export const editSalaryBill = async (data) => {
  return await request.put({ url: '/system/salary-bill-user/edit', data })
}


export const revokeSalaryBill = async (params) => {
  return await request.get({ url: '/system/salary-bill-user/revoke', params })
}

export const deleteSalaryBill = async (id) => {
  return await request.delete({ url: '/system/salary-bill/delete?id='+id })
}
export const getSalaryBill = async (id) => {
  return await request.get({ url: '/system/salary-bill/get?id='+id })
}

export const updateSalaryBill = async (data) => {
  return await request.put({ url: '/system/salary-bill/update', data })
}

export const importSalaryBill = async (data) => {
  return await request.upload({
     url: '/system/salary-bill/import',
     data 
    })
}

export const previewFormSalaryBill = async (id) => {
  return await request.get({ url: '/system/salary-bill/previewForm?id='+id })
}

export const sendAllSalaryBill = async (params) => {
  return await request.get({ url: '/system/salary-bill-user/sendAll',params})
}


export const getImportTemplate = async () => {
  return await request.download({ url: '/system/salary-bill/get-import-template'})
}





// export const createDingMessage = async (data) => {
//   return await request.post({ url: '/oa/ding-message/create', data })
// }

