import request from '@/config/axios'

export interface VersionVO {
  id: number | undefined
  title: string
  type: number
  content: string
  // status: number
  remark: string
  creator: string
  createTime: Date
  coverPic : string
  updatePic : string
  filePaths: string[]
  androidUrl: string
  androidSize: string
  androidName: string
}

// 查询版本信息
export const getVersionPage = (params: PageParam) => {
  return request.get({ url: '/system/version/page', params })
}

// 查询版本详情
export const getVersion = (id: number) => {
  return request.get({ url: '/system/version/get?id=' + id })
}

// 新增版本信息
export const createVersion = (data: VersionVO) => {
  return request.post({ url: '/system/version/create', data })
}

// 修改版本信息
export const updateVersion = (data: VersionVO) => {
  return request.put({ url: '/system/version/update', data })
}

// 删除版本信息
export const deleteVersion = (id: number) => {
  return request.delete({ url: '/system/version/delete?id=' + id })
}
// 获取更新日志信息
export const getVersionLog = () => {
  return request.get({ url: '/system/version/getlog' })
}

// // 推送公告
// export const pushNotice = (id: number) => {
//   return request.post({ url: '/system/notice/push?id=' + id })
// }

export const pushNotice2 = async (id,data) => {
  return await request.post({
    url: `/system/notice/send/`+id,
    data
  })
}
