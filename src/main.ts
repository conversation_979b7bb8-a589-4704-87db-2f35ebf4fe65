// 引入unocss css
import '@/plugins/unocss'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 element-plus
import { setupElementPlus } from '@/plugins/elementPlus'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入全局样式
import '@/styles/index.scss'

// 引入动画
import '@/plugins/animate.css'

// 路由
import router, { setupRouter } from '@/router'
import { setCustomToken } from '@/utils/auth' // 假设这些函数在 auth.js 中
import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
// 权限
import { setupAuth } from '@/directives'

import { createApp } from 'vue'

import App from './App.vue'

import './permission'

import '@/plugins/tongji' // 百度统计
import Logger from '@/utils/Logger'

import VueDOMPurifyHTML from 'vue-dompurify-html' // 解决v-html 的安全隐患
// # merge
import VueClipboard from 'vue-clipboard3';
import {useWFlowStore} from '@/store/modules/wflow'
import ElementPlus from "element-plus";
import * as ElIcons from '@element-plus/icons-vue'
import 'vant/lib/index.css';

import "@/assets/global.css";
import "@/assets/iconfont/iconfont.css"

import Ellipsis from './components/common/Ellipsis.vue'
import WDialog from './components/common/WDialog.vue'
import Tip from './components/common/Tip.vue'
import Avatar from './components/common/Avatar.vue'
import Icon from './components/common/Icon.vue'
// import VueAMap, {initAMapApiLoader} from '@vuemap/vue-amap';
// import '@vuemap/vue-amap/dist/style.css'
import "@/assets/css/oaCommon.css"
/*高德地图组件初始化*/
// initAMapApiLoader({
//   key: 'ebb9f6fb9beaec43fddcb22ab7406386',
//   plugin: [
//     'AMap.Autocomplete',
//   /*  'AMap.ElasticMarker',
//     'AMap.PlaceSearch',
//     "AMap.MapType",*/
//     "AMap.Geocoder",
//     'AMap.Geolocation'
//   ],
// });
// # merge end

const handleMessage = (event) => {
  console.log('handleMessage')
  console.log(event.origin, 'event.origin')
  console.log(event.data, 'event.data')
  // 处理消息
  const data = event.data
  if (data.type === 'urlParamsPC') {
    console.log('Received params:', data.params)
    setCustomToken({
      refreshToken: data.params.refreshToken ? data.params.refreshToken : '',
      accessToken: data.params.accessToken ? data.params.accessToken : '',
    })
  }
  if (data.type === 'clearAllPC') {
    console.log('Received params:', data.params)
    // userStore.loginOut()
    setCustomToken({
      refreshToken:  '',
      accessToken: '',
    })
  }
}
window.addEventListener('message', handleMessage)

// 创建实例
const setupAll = async () => {
  const app = createApp(App)
  // # merge
  window.$vueApp = app
  window.$vCtx = app.config.globalProperties
  import('./utils/Injection')
  // # merge end
  await setupI18n(app)

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  setupAuth(app)

  await router.isReady()

  app.use(VueDOMPurifyHTML)
  // # merge
  //全局注册图标
  // app.use(VueAMap)
  app
   .use(ElementPlus)
  .component('Ellipsis', Ellipsis)
  .component('WDialog', WDialog)
  .component('Tip', Tip)
  .component('Avatar', Avatar)
  .component('Icon', Icon)

  for (const [key, component] of Object.entries(ElIcons)) {
    app.component(`el-icon-${key.toLowerCase()}`, component)
  }
  app.config.globalProperties.routerAppend = (path, pathToAppend) => {
    return path + (path.endsWith('/') ? '' : '/') + pathToAppend
  }
  //注册到全局后可以使用 this.$wflow.xxx访问变量了
  window.$vCtx.$wflow = useWFlowStore() 
  // # merge end
  
  app.mount('#app')
}

setupAll()

Logger.prettyPrimary(`欢迎使用`, import.meta.env.VITE_APP_TITLE)
