<template>
  <div>
    <div v-if="dataList.length > 0" class="fileTenant">
      <div class="tenantDiv" @click="handleOpen" :title="currentObj.name">
        <span>{{ currentObj.name }}</span>
        <icon name="el-icon-caretbottom" />
      </div>
      <div class="file-check" v-if="isOpen" ref="refBox">
        <div v-for="(item, index) in dataList" :key="index" :class="{ isCheck: item.id == currentObj.id }"
          class="file-check-content" @click="checkBind(item)">
          <img class="img2" src="@/assets/imgs/ent.png" />
          <span class="span1">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getTenantId } from '@/utils/auth'
export default {
  data() {
    return {
      dataList: [],
      currentObj: {},
      isOpen: false,
      loading: false,
    }
  },
  watch: {
    isOpen: function (oldV, newV) {
      document.addEventListener('click', this.hideBox)
    },
  },
  created() {
    console.log(localStorage.getItem('tenantList'))
    this.dataList = JSON.parse(localStorage.getItem('tenantList'))
    this.dataList.forEach((item) => {
      if (getTenantId() == item.id) {
        this.currentObj = item
      }
    })
  },
  mounted() {
    document.addEventListener('click', this.hideBox)
  },
  unmounted() {
    document.removeEventListener('click', this.hideBox)
  },
  methods: {
    handleOpen() {
      setTimeout(() => {
        this.isOpen = !this.isOpen
      })
    },
    hideBox(e) {
      const targetDiv = this.$refs.refBox
      if (targetDiv && targetDiv.contains && !targetDiv.contains(e.target)) {
        this.isOpen = false
      }
    },
    checkBind(item) {
      this.currentObj = item
      this.isOpen = false
      this.$emit('success', item.id)
    },
  }
}
</script>

<style lang="less" scope>
.fileTenant {
  background: #fff;

  .tenantDiv {
    display: flex;
    align-items: center;
    cursor: pointer;
    box-sizing: border-box;
    padding: 5px;
    position: relative;
    color: #909399;
    margin-bottom: 10px;
    border-radius: 4px;
    width: fit-content;
    max-width: 100%;
    margin-left: -5px;

    span {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }

  .el-icon-caretbottom {
    margin-left: 20px;
    color: #909399;
  }


  .tenantDiv:hover {
    background: #F3F4F7;
  }

  .file-check {
    position: absolute;
    left: 10px;
    top: 80px;
    width: 350px;
    background: #fff;
    z-index: 10000;
    box-shadow: 0px 2px 16px 0px rgba(0, 0, 0, 0.08), 0px 2px 24px 0px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    padding: 10px;

    .file-check-content {
      display: flex;
      align-items: center;
      padding: 10px;
      position: relative;
      cursor: pointer;

      span {
        display: block;
      }

      .span1 {
        font-size: 14px;
      }
    }

    .img2 {
      margin-right: 14px;
      width: 36px;
      height: 36px;
    }

    .isCheck {
      border: 2px solid #3370FF;
      background: #F3F4F7;
      border-radius: 8px;
    }

    .file-check-content:hover {
      background: #F3F4F7;
      border-radius: 8px;
    }
  }
}
</style>